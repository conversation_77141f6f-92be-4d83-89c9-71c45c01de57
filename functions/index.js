const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
// const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");
const nodemailer = require('nodemailer');
const { FieldValue } = require("firebase-admin/firestore");
const {
  onDocumentWritten,
  // onDocumentCreated,
  // onDocumentUpdated,
  // onDocumentDeleted,
  // Change,
  // FirestoreEvent
} = require("firebase-functions/v2/firestore");
// const dateFuncs = require('date-and-time');
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// exports.helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });


var transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 465,
  secure: true,
  auth: {
    user: '<EMAIL>',
    pass: 'hthtfwqggxyprhal'
    // pass: 'zniorhjmhmatlzfd'
  }
});


exports.createFranchise = onCall(async (request) => {
  // let data = req.body;
  try {
    console.log("IN CREATE Franchise");
    if (!request.auth)
      return { status: "error", code: 401, message: "Not signed in" };
    let response1 = await auth.createUser({
      email: request.data.email,
      password: request.data.password,
      // phoneNumber: request.data.contactNumber,
    });
    console.log("Successfully created new user:", response1.uid);
    try {
      let response2 = await db
        .collection("Franchises")
        .doc(response1.uid)
        .create({
          ownerName: request.data.ownerName,
          garageName: request.data.garageName,
          email: request.data.email,
          password: request.data.password,
          address: request.data.address,
          contactNumber: request.data.contactNumber,
          createdAt: FieldValue.serverTimestamp(),
          notavailable: false,
        });
      console.log("User Data saved Successfully");
      return { success: true, msg: "Franchise created successfully!" };
    } catch (error) {
      console.log("Failed to create user doc, need to delete this user again!");
      await auth.deleteUser(response1.uid);
      console.log("User deleted successfully!");
      return { success: false, msg: error };
    }
  } catch (error) {
    console.log("Error creating new user:", error);
    return {
      success: false,
      msg: error["errorInfo"]["message"],
      code: error["errorInfo"]["code"],
    };
  }
});

exports.deleteFranchise = onCall(async (request) => {
  try {
    console.log(request.data);
    try {
      await admin.auth().deleteUser(request.data.uid);
      await admin
        .firestore()
        .collection("Franchises")
        .doc(request.data.uid)
        .delete()
        .then((val) => {
          console.log(`Deleted ${request.data.uid}`);
          return { success: true };
        });
    } catch (error) {
      console.log(`Error deleting ${request.data.uid}`);
    }
  } catch (error) {
    console.log(error);
    return { success: false };
  }
});

exports.notifies = onDocumentWritten("Notification/{userId}", async (event) => {
  try {
    const data = event.data.after.data();
    console.log(data.test == true ? "Test Noti..." : "Global Noti...");
    console.log("data.topic");
    console.log(data.topic);
    const payload = {
      // topic: 'test',
      topic: data.test == true ? "test" : data.topic,
      // topic: data.test !=null? 'test' : 'global',
      notification: {
        title: data.title,
        body: data.desc,
      },
      android: {
        priority: "high",
        notification: {
          channel_id: "speedforce",
        },
      },
      apns: {
        headers: {
          "apns-priority": "10",
        },
        payload: {
          aps: {
            alert: {
              title: data.title,
              body: data.desc,
            },
            sound: "default",
          },
        },
      },
      data: {
        title: data.title,
        body: data.desc,
      },
    };
    messaging
      .send(payload)
      .then((response) => {
        // Response is a message ID string.
        console.log("Successfully sent message:", response);
        return { success: true };
      })
      .catch((error) => {
        return { error: error.code };
      });
  } catch (error) {
    console.log(error);
  }
});

exports.notifyUser = onDocumentWritten(
  "userNotifies/{userId}",
  async (event) => {
    try {
      const data = event.data.after.data();
      // const data = event.data();
      var user = await admin
        .firestore()
        .collection("users")
        .doc(data.uid)
        .get();
      if (user.exists) {
        const payload = {
          tokens: user.data()["tokens"],
          notification: {
            title: data.title,
            body: data.desc,
          },
          android: {
            notification: {
              channel_id: "speedforce",
            },
          },
          data: {},
        };
        messaging
          .sendEachForMulticast(payload)
          .then((response) => {
            // Response is a message ID string.
            console.log("Successfully sent message:", response);
            return { success: true };
          })
          .catch((error) => {
            return { error: error.code };
          });
      }
    } catch (error) {
      console.log(error);
    }
  }
);


exports.sendOtpEmail = onCall(async (request) => {
  const emailText = `<html>
  <body>
  <p>${request.data.otp} is your OTP. Valid for 5 minutes.</p>
  </body>
  </html>`;
  sendEmailToUser(request.data.email, "Speed force auth", emailText);
});


async function sendEmailToUser(to, subject, html) {
  try {
    const mailOptions = {
      from: {
        name: 'Speed Force Tech',
        address: '<EMAIL>'
      },
      to: to,
      subject: subject,
      html: html
    };
    return transporter.sendMail(mailOptions, (error, data) => {
      if (error) {
        console.log(error)
        return
      }
      console.log("Sent!")
    });
  } catch (error) {
    console.log(error);
  }
}

exports.notiToUser = onCall(async (request) => {
  sendNotificationToUser(
    request.body["title"],
    request.body["msg"],
    request.body["uid"]
  );
});

exports.notiToAdmin = onCall(async (request) => {
  sendNotificationToAdmin(request.body["title"], request.body["msg"]);
});

async function sendNotificationToUser(title, message, uid) {
  try {
    var user = await admin.firestore().collection("users").doc(uid).get();
    if (user.exists) {
      const payload = {
        tokens: user.data()["tokens"],
        notification: {
          title: title,
          body: message,
        },
        android: {
          notification: {
            channel_id: "speedforce",
          },
        },
        data: {},
      };
      messaging
        .sendEachForMulticast(payload)
        .then((response) => {
          // Response is a message ID string.
          console.log("Successfully sent message:", response);
          return { success: true };
        })
        .catch((error) => {
          return { error: error.code };
        });
    }
  } catch (error) {
    console.log(error);
  }
}

async function sendNotificationToAdmin(title, message) {
  try {
    const payload = {
      topic: "admin",
      notification: {
        title: title,
        body: message,
      },
      android: {
        notification: {
          channel_id: "speedforce",
        },
      },
      data: {},
    };
    messaging
      .send(payload)
      .then((response) => {
        // Response is a message ID string.
        console.log("Successfully sent message:", response);
        return { success: true };
      })
      .catch((error) => {
        return { error: error.code };
      });
  } catch (error) {
    console.log(error);
  }
}

/*            final data =
                                                              <String, dynamic>{
                                                            "name":
                                                                nameController
                                                                    .text,
                                                            "phoneNumber":
                                                                phone,
                                                          };
                                                          final result =
                                                              await FBFunctions
                                                                  .ff
                                                                  .httpsCallable(
                                                                      'createStaff')
                                                                  .call(data);
                                                                  
                                                                  
                                                                  */
