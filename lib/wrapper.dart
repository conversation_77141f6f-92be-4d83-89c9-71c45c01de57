import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/dashboard_drawer.dart';

class Wrapper extends StatelessWidget {
  const Wrapper({super.key, required this.child});
  final Widget child;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
            child: const DashboardDrawer(),
          ),
          const VerticalDivider(width: 0, thickness: 0),
          Expanded(child: child),
        ],
      ),
    );
  }
}
