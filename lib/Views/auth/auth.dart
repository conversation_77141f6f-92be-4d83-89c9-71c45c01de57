import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../shared/router.dart';

// const String email = '<EMAIL>';

class SignInPage extends StatelessWidget {
  const SignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final providers = [EmailAuthProvider()];
    return Scaffold(
      body: SignInScreen(
        // email: email,
        sideBuilder: (context, constraints) => Container(
          color: Colors.white,
          child: Center(
            child: SizedBox(
              width: 270,
              child: Image.asset(
                'assets/logo.png',
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
        providers: providers,
        showAuthActionSwitch: false,
        actions: [
          AuthStateChangeAction<SignedIn>((context, state) {
            if (state.user != null) context.push(homeRoute);
          }),
        ],
      ),
    );
  }
}
