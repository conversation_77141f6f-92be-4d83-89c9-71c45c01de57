import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/shared/firebase.dart';

Future<Map<String, dynamic>> nextVehicleFetch(DocumentSnapshot<Object?> lastDoc,
    int currentPage, bool fromAllVehicles, String? selectedworkshop) async {
  try {
    if (fromAllVehicles) {
      final data = await FBFireStore.customersvehicles
          .orderBy('registrationNumber')
          .startAfterDocument(lastDoc)
          .limit(30)
          .get();
      return {
        'data': data.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.length < 30 ? null : data.docs[data.docs.length - 1],
        'currentPage': currentPage + 1,
        'firstDoc': data.docs.isNotEmpty ? data.docs[0] : null,
      };
    } else {
      final data = await FBFireStore.customersvehicles
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('registrationNumber')
          .startAfterDocument(lastDoc)
          .limit(30)
          .get();
      return {
        'data': data.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.length < 30 ? null : data.docs[data.docs.length - 1],
        'currentPage': currentPage + 1,
        'firstDoc': data.docs.isNotEmpty ? data.docs[0] : null,
      };
    }
  } catch (e) {
    debugPrint(e.toString());
    return {
      'data': <CustomerVehicleModel>[],
      'lastDoc': null,
      'currentPage': currentPage,
      'firstDoc': null,
    };
  }
}

Future<Map<String, dynamic>> previousVehicleFetch(DocumentSnapshot<Object?> firstDoc,
    int currentPage, bool fromAllVehicles, String? selectedworkshop) async {
  try {
    if (fromAllVehicles) {
      final data = await FBFireStore.customersvehicles
          .orderBy('registrationNumber')
          .endBeforeDocument(firstDoc)
          .limitToLast(30)
          .get();

      if (data.docs.isEmpty) {
        return {
          'data': <CustomerVehicleModel>[],
          'lastDoc': null,
          'firstDoc': null,
          'currentPage': currentPage,
        };
      }

      return {
        'data': data.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList(),
        'lastDoc': data.docs[data.docs.length - 1],
        'firstDoc': data.docs[0],
        'currentPage': currentPage - 1,
      };
    } else {
      final data = await FBFireStore.customersvehicles
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('registrationNumber')
          .endBeforeDocument(firstDoc)
          .limitToLast(30)
          .get();

      if (data.docs.isEmpty) {
        return {
          'data': <CustomerVehicleModel>[],
          'lastDoc': null,
          'firstDoc': null,
          'currentPage': currentPage,
        };
      }

      return {
        'data': data.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList(),
        'lastDoc': data.docs[data.docs.length - 1],
        'firstDoc': data.docs[0],
        'currentPage': currentPage - 1,
      };
    }
  } catch (e) {
    debugPrint(e.toString());
    return {
      'data': <CustomerVehicleModel>[],
      'lastDoc': null,
      'currentPage': currentPage,
      'firstDoc': null,
    };
  }
}
