import 'package:flutter/material.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import '../../../models/franchise_details.dart';

class VehicleTile extends StatefulWidget {
  const VehicleTile({
    super.key,
    required this.vehicles,
    required this.currentPage,
  });

  final List<CustomerVehicleModel> vehicles;
  final int currentPage;

  @override
  State<VehicleTile> createState() => _VehicleTileState();
}

class _VehicleTileState extends State<VehicleTile> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.vehicles.length,
      itemBuilder: (context, index) {
        return SingleVehicleDetail(
          index: (widget.currentPage * 30) + index,
          cusvehiclemodel: widget.vehicles[index],
        );
      },
    );
  }
}

class SingleVehicleDetail extends StatefulWidget {
  const SingleVehicleDetail({
    super.key,
    required this.index,
    required this.cusvehiclemodel,
  });

  final int index;
  final CustomerVehicleModel cusvehiclemodel;

  @override
  State<SingleVehicleDetail> createState() => _SingleVehicleDetailState();
}

class _SingleVehicleDetailState extends State<SingleVehicleDetail> {
  CustomersModel? customer;
  FranchiseDetails? franchise;
  getcusdata() async {
    final data = await FBFireStore.customers
        .doc(widget.cusvehiclemodel.customerId)
        .get();
    if (data.data() != null) {
      customer = CustomersModel.fromSnap(data);
    } else {
      customer = null;
    }

    setState(() {});
  }

  getfranchisedata() async {
    final data = await FBFireStore.franchises
        .doc(widget.cusvehiclemodel.franchiseId)
        .get();
    if (data.data() != null) {
      franchise = FranchiseDetails.fromSnap(data);
    } else {
      franchise = null;
    }

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getcusdata();
    getfranchisedata();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: widget.index % 2 != 0
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(4), color: Colors.grey[200])
          : null,
      child: Row(
        children: [
          SizedBox(
              width: 70,
              child: Text((widget.index + 1).toString(),
                  textAlign: TextAlign.center)),
          SizedBox(
              width: 150,
              child: Text(
                  overflow: TextOverflow.ellipsis,
                  widget.cusvehiclemodel.registrationNumber ?? "")),
          const SizedBox(width: 5),
          SizedBox(
              width: 150,
              child: Text(
                  overflow: TextOverflow.ellipsis, customer?.username ?? "")),
          const SizedBox(width: 5),
          SizedBox(
              width: 200,
              child: Text(
                overflow: TextOverflow.ellipsis,
                franchise?.garageName ?? "",
                maxLines: 1,
              )),
          const SizedBox(width: 5),
          SizedBox(
              width: 100,
              child: Text(
                // overflow: TextOverflow.ellipsis,
                widget.cusvehiclemodel.make ?? "",
                maxLines: 1,
              )),
          const SizedBox(width: 5),
          SizedBox(
              width: 100,
              child: Text(
                // overflow: TextOverflow.ellipsis,
                widget.cusvehiclemodel.model ?? "",
                maxLines: 1,
              )),
        ],
      ),
    );
  }
}
