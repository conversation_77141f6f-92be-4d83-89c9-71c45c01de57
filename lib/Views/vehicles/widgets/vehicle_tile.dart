import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import '../../../models/franchise_details.dart';

class VehicleTile extends StatefulWidget {
  const VehicleTile({
    super.key,
  });

  @override
  State<VehicleTile> createState() => _VehicleTileState();
}

class _VehicleTileState extends State<VehicleTile> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return ListView.builder(
        shrinkWrap: true,
        itemCount: ctrl.cusvehicle.length,
        itemBuilder: (context, index) {
          // final fdata = ctrl.franchises.firstWhereOrNull((element) =>
          //     element.docID == widget.customers[index].franchiseId);

          return SingleVehicleDetail(
            index: index,
            cusvehiclemodel: ctrl.cusvehicle[index],
          );
        },
      );
    });
  }
}

class SingleVehicleDetail extends StatefulWidget {
  const SingleVehicleDetail({
    super.key,
    required this.index,
    required this.cusvehiclemodel,
  });

  final int index;
  final CustomerVehicleModel cusvehiclemodel;

  @override
  State<SingleVehicleDetail> createState() => _SingleVehicleDetailState();
}

class _SingleVehicleDetailState extends State<SingleVehicleDetail> {
  CustomersModel? customer;
  FranchiseDetails? franchise;
  getcusdata() async {
    customer = await FBFireStore.customers
        .doc(widget.cusvehiclemodel.customerId)
        .get()
        .then(
          (value) => CustomersModel.fromSnap(value),
        );
    setState(() {});
  }

  getfranchisedata() async {
    franchise = await FBFireStore.franchises
        .doc(widget.cusvehiclemodel.franchiseId)
        .get()
        .then(
          (value) => FranchiseDetails.fromSnap(value),
        );
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getcusdata();
    getfranchisedata();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: widget.index % 2 != 0
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(4), color: Colors.grey[200])
          : null,
      child: Row(
        children: [
          SizedBox(
              width: 70,
              child: Text((widget.index + 1).toString(),
                  textAlign: TextAlign.center)),
          SizedBox(
              width: 150,
              child: Text(
                  overflow: TextOverflow.ellipsis,
                  widget.cusvehiclemodel.registrationNumber ?? "")),
          // const SizedBox(width: 5),
          // SizedBox(
          //     width: 150,
          //     child: Text(
          //         overflow: TextOverflow.ellipsis,
          //         ctrl.cusvehicle[index].chasisNumber ?? "")),
          const SizedBox(width: 5),
          SizedBox(
              width: 150,
              child: Text(
                  overflow: TextOverflow.ellipsis, customer?.username ?? "")),
          const SizedBox(width: 5),
          SizedBox(
              width: 200,
              child: Text(
                overflow: TextOverflow.ellipsis,
                franchise?.garageName ?? "",
                maxLines: 1,
              )),
          const SizedBox(width: 5),
          SizedBox(
              width: 100,
              child: Text(
                // overflow: TextOverflow.ellipsis,
                widget.cusvehiclemodel.make ?? "",
                maxLines: 1,
              )),
          const SizedBox(width: 5),
          SizedBox(
              width: 100,
              child: Text(
                // overflow: TextOverflow.ellipsis,
                widget.cusvehiclemodel.model ?? "",
                maxLines: 1,
              )),
          // Expanded(
          //   child: Wrap(
          //     alignment: WrapAlignment.end,
          //     children: [
          //       IconButton(
          //           onPressed: () {
          //             addfranchiseform(
          //                 context, false, _.franchises[index]);
          //           },
          //           icon: const Icon(Icons.edit)),
          //       Transform.scale(
          //         scale: .7,
          //         child: CupertinoSwitch(
          //           value: !_.franchises[index].notavailable,
          //           onChanged: (valueeee) {
          //             FBFireStore.franchises
          //                 .doc(_.franchises[index].docID)
          //                 .update({'notavailable': !valueeee});
          //           },
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // IconButton(
          //     onPressed: () => showDialog(
          //           context: context,
          //           builder: (BuildContext context) {
          //             return AlertDialog(
          //               actions: [
          //                 Padding(
          //                   padding: const EdgeInsets.all(0),
          //                   child: Column(
          //                     children: [
          //                       const SizedBox(height: 25),
          //                       const Text("Are You Sure?"),
          //                       const SizedBox(height: 10),
          //                       Row(
          //                         mainAxisAlignment:
          //                             MainAxisAlignment.center,
          //                         children: [
          //                           TextButton(
          //                             onPressed: () async {
          //                               FBFireStore.franchises
          //                                   .doc(_.franchises[index]
          //                                       .docID)
          //                                   .delete();
          //                               context.pop();
          //                             },
          //                             child: const Text("YES"),
          //                           ),
          //                           TextButton(
          //                             onPressed: () {
          //                               context.pop();
          //                             },
          //                             child: const Text("NO"),
          //                           )
          //                         ],
          //                       )
          //                     ],
          //                   ),
          //                 ),
          //               ],
          //             );
          //           },
          //         ),
          //     icon: const Icon(Icons.delete)),
        ],
      ),
    );
  }
}
