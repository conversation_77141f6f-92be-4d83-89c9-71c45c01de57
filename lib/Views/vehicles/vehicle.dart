import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vtable_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vehicle_methods.dart';
import 'package:speed_force_admin/Views/franchises/widgets/pagination_ui.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import 'widgets/vehicle_tile.dart';

class Vehicles extends StatefulWidget {
  const Vehicles({super.key});

  @override
  State<Vehicles> createState() => _VehiclesState();
}

class _VehiclesState extends State<Vehicles> {
  final searchController = TextEditingController();
  Timer? _debounceTimer;

  List<CustomerVehicleModel> vehicleList = [];
  List<CustomerVehicleModel> vehicleSearchList = [];
  DocumentSnapshot<Object?>? lastDoc;
  DocumentSnapshot<Object?>? firstDoc;
  int currentPage = 0;
  bool isLoading = false;
  String? errorMessage;
  int? totalVehicles;

  fetchVehicleData() async {
    try {
      totalVehicles = (await FBFireStore.customersvehicles.count().get()).count;
      if (mounted) {
        setState(() {
          isLoading = true;
          errorMessage = null;
        });
      }

      final firstData = await FBFireStore.customersvehicles
          .limit(30)
          .orderBy('registrationNumber')
          .get();

      vehicleList = firstData.docs.map((e) {
        return e.exists
            ? CustomerVehicleModel.fromSnap(e)
            : CustomerVehicleModel();
      }).toList();

      lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
      firstDoc = firstData.docs.isNotEmpty ? firstData.docs.first : null;

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
          errorMessage = 'Error loading vehicles: ${e.toString()}';
        });
      }
      debugPrint('Error fetching vehicle data: $e');
    }
  }

  void _onSearchChanged(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      getVehicleFilteredList(value);
    });
  }

  getVehicleFilteredList(String value) async {
    if (value.trim().isEmpty) {
      if (mounted) {
        setState(() {
          vehicleSearchList = [];
        });
      }
      return;
    }

    try {
      if (mounted) {
        setState(() {
          isLoading = true;
          errorMessage = null;
        });
      }

      final vehicleQuery = await FBFireStore.customersvehicles
          .where('registrationNumber',
              isGreaterThanOrEqualTo: value.toUpperCase())
          .where('registrationNumber',
              isLessThanOrEqualTo: "${value.toUpperCase()}\uf7ff")
          .limit(30)
          .get();

      List<CustomerVehicleModel> vehicleResults = vehicleQuery.docs
          .map((e) => CustomerVehicleModel.fromSnap(e))
          .toList();

      final customerQuery = await FBFireStore.customers
          .where('lowername', isGreaterThanOrEqualTo: value.toLowerCase())
          .where('lowername',
              isLessThanOrEqualTo: "${value.toLowerCase()}\uf7ff")
          .limit(50)
          .get();

      List<String> customerIds =
          customerQuery.docs.map((doc) => doc.id).toList();

      List<CustomerVehicleModel> customerVehicleResults = [];
      if (customerIds.isNotEmpty) {
        for (int i = 0; i < customerIds.length; i += 10) {
          final batch = customerIds.skip(i).take(10).toList();
          final vehiclesByCustomer = await FBFireStore.customersvehicles
              .where('customerId', whereIn: batch)
              .get();

          customerVehicleResults.addAll(vehiclesByCustomer.docs
              .map((e) => CustomerVehicleModel.fromSnap(e)));
        }
      }

      final allResults = <String, CustomerVehicleModel>{};

      for (var vehicle in vehicleResults) {
        if (vehicle.vehicleId != null) {
          allResults[vehicle.vehicleId!] = vehicle;
        }
      }

      for (var vehicle in customerVehicleResults) {
        if (vehicle.vehicleId != null) {
          allResults[vehicle.vehicleId!] = vehicle;
        }
      }

      vehicleSearchList = allResults.values.toList();

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
          errorMessage = 'Error searching vehicles: ${e.toString()}';
          vehicleSearchList = [];
        });
      }
      debugPrint('Error in vehicle search: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    fetchVehicleData();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: "Vehicles (${totalVehicles ?? ''})",
            onPressed: () {},
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 450),
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                    controller: searchController,
                    cursorHeight: 20,
                    onChanged: (value) {
                      currentPage = 0;
                      _onSearchChanged(value.trim());
                    },
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(),
                      fillColor: Colors.grey.shade100,
                      filled: true,
                      prefixIcon: const Icon(
                        Icons.search,
                        size: 22,
                        color: Colors.blue,
                      ),
                      border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(7)),
                      hintText: 'Search by vehicle number or customer name',
                      hintStyle: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
              if (searchController.text.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: TextButton.icon(
                    onPressed: () {
                      searchController.clear();
                      if (mounted) {
                        setState(() {
                          vehicleSearchList = [];
                          currentPage = 0;
                          errorMessage = null;
                        });
                      }
                    },
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                  ),
                ),
            ],
          ),

          // Show search info

          const SizedBox(height: 30),

          if (errorMessage != null)
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                border: Border.all(color: Colors.red.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      errorMessage!,
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      if (mounted) {
                        setState(() => errorMessage = null);
                      }
                    },
                    icon: Icon(Icons.close, color: Colors.red.shade600),
                    iconSize: 20,
                  ),
                ],
              ),
            ),

          const VehiclesTableHeader(),

          if (isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(50),
                child: CircularProgressIndicator(),
              ),
            )
          else
            VehicleTile(
              vehicles: searchController.text.isNotEmpty
                  ? vehicleSearchList
                  : vehicleList,
              currentPage: currentPage,
            ),

          const SizedBox(height: 20),
          if (searchController.text.isEmpty)
            PaginationArrowsExpiry(
              previousBlocked: currentPage == 0,
              nextsBlocked: lastDoc == null,
              onPrevious: () async {
                if (currentPage > 0 && firstDoc != null) {
                  try {
                    if (mounted) {
                      setState(() {
                        isLoading = true;
                        errorMessage = null;
                      });
                    }

                    final data =
                        await previousVehicleFetch(firstDoc!, currentPage);
                    vehicleList = data['data'];
                    lastDoc = data['lastDoc'];
                    firstDoc = data['firstDoc'];
                    currentPage = data['currentPage'];

                    if (mounted) {
                      setState(() {
                        isLoading = false;
                      });
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        isLoading = false;
                        errorMessage =
                            'Error loading previous page: ${e.toString()}';
                      });
                    }
                    debugPrint('Error in previous page: $e');
                  }
                }
              },
              onNext: () async {
                if (lastDoc != null) {
                  try {
                    if (mounted) {
                      setState(() {
                        isLoading = true;
                        errorMessage = null;
                      });
                    }

                    final data = await nextVehicleFetch(lastDoc!, currentPage);
                    vehicleList = data['data'];
                    lastDoc = data['lastDoc'];
                    firstDoc = data['firstDoc'];
                    currentPage = data['currentPage'];

                    if (mounted) {
                      setState(() {
                        isLoading = false;
                      });
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        isLoading = false;
                        errorMessage =
                            'Error loading next page: ${e.toString()}';
                      });
                    }
                    debugPrint('Error in next page: $e');
                  }
                }
              },
            )
        ],
      ),
    );
  }
}
