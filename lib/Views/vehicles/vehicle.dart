import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vtable_header.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';

import 'widgets/vehicle_tile.dart';

class Vehicles extends StatefulWidget {
  const Vehicles({super.key});

  @override
  State<Vehicles> createState() => _VehiclesState();
}

class _VehiclesState extends State<Vehicles> {
  bool loading = false;
  final ctrl = Get.find<HomeCtrl>();

  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    List<CustomerVehicleModel> sfiltered = ctrl.cusvehicle
        .where((element) =>
            element.registrationNumber?.contains(searchController.text) ?? true)
        .toList();

    return SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          PageHeaderWithButton(
            title: "Vehicles",
            onPressed: () {},
          ),
          const SizedBox(height: 20),
          SearchField(
            searchController: searchController,
            onChanged: (p0) {
              sfiltered = ctrl.cusvehicle
                  .where((element) => element.registrationNumber!.contains(p0))
                  .toList();
              setState(() {});
            },
          ),
          const SizedBox(height: 45),
          const VehiclesTableHeader(),
          loading
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.only(top: 50),
                    child: CircularProgressIndicator(),
                  ),
                )
              : const VehicleTile()
        ]));
  }
}
