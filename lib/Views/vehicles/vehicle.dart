import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vtable_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vehicle_methods.dart';
import 'package:speed_force_admin/Views/franchises/widgets/pagination_ui.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import 'widgets/vehicle_tile.dart';

class Vehicles extends StatefulWidget {
  const Vehicles({super.key});

  @override
  State<Vehicles> createState() => _VehiclesState();
}

class _VehiclesState extends State<Vehicles> {
  String? selectedworkshop;
  final searchController = TextEditingController();

  List<CustomerVehicleModel> vehicleList = [];
  List<CustomerVehicleModel> vehicleSearchList = [];
  DocumentSnapshot<Object?>? lastDoc;
  DocumentSnapshot<Object?>? firstDoc;
  int currentPage = 0;

  fetchVehicleData() async {
    final firstData = await FBFireStore.customersvehicles
        .limit(30)
        .orderBy('registrationNumber')
        .get();
    vehicleList = firstData.docs.map((e) {
      return CustomerVehicleModel.fromSnap(e);
    }).toList();
    lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
    firstDoc = firstData.docs.isNotEmpty ? firstData.docs.first : null;
    setState(() {});
  }

  getVehicleFilteredList(String value) async {
    try {
      final firstData = selectedworkshop == null
          ? (await FBFireStore.customersvehicles
              .where('registrationNumber', isGreaterThanOrEqualTo: value)
              .where('registrationNumber', isLessThanOrEqualTo: "$value\uf7ff")
              .limit(30)
              .get())
          : (await FBFireStore.customersvehicles
              .where('franchiseId', isEqualTo: selectedworkshop)
              .where('registrationNumber', isGreaterThanOrEqualTo: value)
              .where('registrationNumber', isLessThanOrEqualTo: "$value\uf7ff")
              .limit(30)
              .get());

      vehicleSearchList =
          firstData.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getWorkshopVehicleList(String value) async {
    try {
      final firstData = await FBFireStore.customersvehicles
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('registrationNumber')
          .limit(30)
          .get();
      lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
      firstDoc = firstData.docs.isNotEmpty ? firstData.docs.first : null;
      vehicleList =
          firstData.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    fetchVehicleData();
  }

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: "Vehicles",
            onPressed: () {},
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              SearchField(
                searchController: searchController,
                onChanged: (value) {
                  currentPage = 0;
                  getVehicleFilteredList(
                      searchController.text.trim().toLowerCase());
                },
              ),
              const SizedBox(width: 10),
              SizedBox(
                width: 200,
                child: DropdownButtonFormField<String>(
                  isExpanded: true,
                  value: selectedworkshop,
                  decoration: const InputDecoration(
                    labelText: "Select Workshop",
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    ...List.generate(
                      ctrl.franchises.length,
                      (index) {
                        return DropdownMenuItem(
                            value: ctrl.franchises[index].docID,
                            child:
                                Text(ctrl.franchises[index].garageName ?? ""));
                      },
                    ),
                  ],
                  onChanged: (value) async {
                    if (value == null) {
                      return;
                    }
                    currentPage = 0;
                    vehicleList = [];
                    vehicleSearchList = [];
                    lastDoc = null;
                    firstDoc = null;
                    selectedworkshop = value;
                    getWorkshopVehicleList(value);
                  },
                ),
              ),
              const SizedBox(width: 10),
              TextButton(
                  onPressed: () {
                    currentPage = 0;
                    vehicleList = [];
                    vehicleSearchList = [];
                    lastDoc = null;
                    firstDoc = null;
                    selectedworkshop = null;
                    searchController.clear();
                    fetchVehicleData();
                  },
                  child: const Text('Clear'))
            ],
          ),
          const SizedBox(height: 45),
          const VehiclesTableHeader(),
          VehicleTile(
            vehicles: searchController.text.isNotEmpty
                ? vehicleSearchList
                : vehicleList,
            currentPage: currentPage,
          ),
          const SizedBox(height: 20),
          if (searchController.text.isEmpty)
            PaginationArrowsExpiry(
              previousBlocked: currentPage == 0,
              nextsBlocked: lastDoc == null,
              onPrevious: () async {
                if (currentPage > 0 && firstDoc != null) {
                  final data = await previousVehicleFetch(firstDoc!,
                      currentPage, selectedworkshop == null, selectedworkshop);
                  vehicleList = data['data'];
                  lastDoc = data['lastDoc'];
                  firstDoc = data['firstDoc'];
                  currentPage = data['currentPage'];
                  setState(() {});
                }
              },
              onNext: () async {
                if (lastDoc != null) {
                  final data = await nextVehicleFetch(lastDoc!, currentPage,
                      selectedworkshop == null, selectedworkshop);
                  vehicleList = data['data'];
                  lastDoc = data['lastDoc'];
                  firstDoc = data['firstDoc'];
                  currentPage = data['currentPage'];
                  setState(() {});
                }
              },
            )
        ],
      ),
    );
  }
}
