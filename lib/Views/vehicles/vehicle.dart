import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vtable_header.dart';
import 'package:speed_force_admin/Views/vehicles/widgets/vehicle_methods.dart';
import 'package:speed_force_admin/Views/franchises/widgets/pagination_ui.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import 'widgets/vehicle_tile.dart';

class Vehicles extends StatefulWidget {
  const Vehicles({super.key});

  @override
  State<Vehicles> createState() => _VehiclesState();
}

class _VehiclesState extends State<Vehicles> {
  final searchController = TextEditingController();

  List<CustomerVehicleModel> vehicleList = [];
  List<CustomerVehicleModel> vehicleSearchList = [];
  DocumentSnapshot<Object?>? lastDoc;
  DocumentSnapshot<Object?>? firstDoc;
  int currentPage = 0;

  fetchVehicleData() async {
    final firstData = await FBFireStore.customersvehicles
        .limit(30)
        .orderBy('registrationNumber')
        .get();
    vehicleList = firstData.docs.map((e) {
      return CustomerVehicleModel.fromSnap(e);
    }).toList();
    lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
    firstDoc = firstData.docs.isNotEmpty ? firstData.docs.first : null;
    setState(() {});
  }

  getVehicleFilteredList(String value) async {
    try {
      final firstData = await FBFireStore.customersvehicles
          .where('registrationNumber', isGreaterThanOrEqualTo: value)
          .where('registrationNumber', isLessThanOrEqualTo: "$value\uf7ff")
          .limit(30)
          .get();

      vehicleSearchList =
          firstData.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    fetchVehicleData();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: "Vehicles",
            onPressed: () {},
          ),
          const SizedBox(height: 20),
          SearchField(
            searchController: searchController,
            onChanged: (value) {
              currentPage = 0;
              getVehicleFilteredList(
                  searchController.text.trim().toLowerCase());
            },
          ),
          const SizedBox(height: 45),
          const VehiclesTableHeader(),
          VehicleTile(
            vehicles: searchController.text.isNotEmpty
                ? vehicleSearchList
                : vehicleList,
            currentPage: currentPage,
          ),
          const SizedBox(height: 20),
          if (searchController.text.isEmpty)
            PaginationArrowsExpiry(
              previousBlocked: currentPage == 0,
              nextsBlocked: lastDoc == null,
              onPrevious: () async {
                if (currentPage > 0 && firstDoc != null) {
                  final data = await previousVehicleFetch(
                      firstDoc!, currentPage, true, null);
                  vehicleList = data['data'];
                  lastDoc = data['lastDoc'];
                  firstDoc = data['firstDoc'];
                  currentPage = data['currentPage'];
                  setState(() {});
                }
              },
              onNext: () async {
                if (lastDoc != null) {
                  final data =
                      await nextVehicleFetch(lastDoc!, currentPage, true, null);
                  vehicleList = data['data'];
                  lastDoc = data['lastDoc'];
                  firstDoc = data['firstDoc'];
                  currentPage = data['currentPage'];
                  setState(() {});
                }
              },
            )
        ],
      ),
    );
  }
}
