import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import '../../models/customers_model.dart';
import '../../models/franchise_details.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  TextEditingController titleCtrl = TextEditingController();
  TextEditingController descCtrl = TextEditingController();
  int? _selectedValue;
  // List<FranchiseDetails> franchiseList = [];
  // List<CustomersModel> customersList = [];

  @override
  void initState() {
    super.initState();
    // final hctrl = Get.find<HomeCtrl>();
    // franchiseList = hctrl.franchises;
    // customersList = hctrl.
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(50),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(title: "Notifications", onPressed: () {}),
          const SizedBox(height: 50),
          Container(
            constraints: const BoxConstraints(maxHeight: 300, maxWidth: 800),
            child: Column(
              children: [
                TextFormField(
                  controller: titleCtrl,
                  decoration: InputDecoration(
                      label: const Text("Title"),
                      border: OutlineInputBorder(
                          borderSide:
                              const BorderSide(width: 2, color: Colors.black),
                          borderRadius: BorderRadius.circular(2))),
                ),
                const SizedBox(height: 20),
                TextFormField(
                  controller: descCtrl,
                  decoration: InputDecoration(
                      label: const Text("Description"),
                      border: OutlineInputBorder(
                          borderSide:
                              const BorderSide(width: 2, color: Colors.black),
                          borderRadius: BorderRadius.circular(2))),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    SizedBox(
                      height: 50,
                      child: DropdownButton(
                        value: _selectedValue,
                        focusColor: Colors.transparent,
                        hint: const Text("SELECT USER"),
                        items: const [
                          DropdownMenuItem(
                            value: 1,
                            child: Text("Franchises"),
                          ),
                          DropdownMenuItem(
                            value: 2,
                            child: Text("Customers"),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedValue = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 20),
                    const Text(
                      "Test",
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(width: 5),
                    Switch(
                      activeColor: Colors.green,
                      inactiveThumbColor: Colors.red,
                      value: true,
                      onChanged: (value) {},
                    ),
                    const SizedBox(width: 20),
                    ElevatedButton(
                        style: ButtonStyle(
                            backgroundColor:
                                const WidgetStatePropertyAll(Colors.green),
                            elevation: const WidgetStatePropertyAll(1),
                            shape: WidgetStatePropertyAll(
                                ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(2),
                            ))),
                        onPressed: () async {
                          await FBFireStore.notifications.add({
                            'userType': _selectedValue,
                            'title': titleCtrl.text,
                            'description': descCtrl.text,
                            'createdAt': FieldValue.serverTimestamp(),
                            'test': true
                          });
                        },
                        child: const Text(
                          "Push Notification",
                          style: TextStyle(color: Colors.white),
                        ))
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
