import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/franchises/widgets/add_franchise_form.dart';
import 'package:speed_force_admin/Views/franchises/widgets/franchise_tile.dart';
import 'package:speed_force_admin/Views/franchises/widgets/ftable_header.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'package:speed_force_admin/shared/methods.dart';

class Franchises extends StatefulWidget {
  const Franchises({super.key});

  @override
  State<Franchises> createState() => _FranchisesState();
}

class _FranchisesState extends State<Franchises> {
  int _currentPage = 0;
  final NumberPaginatorController paginatorController =
      NumberPaginatorController();

  @override
  void initState() {
    super.initState();
  }

  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      // List<FranchiseDetails> originallist = ctrl.franchises;
      List<FranchiseDetails> filteredList = ctrl.franchises
          .where((element) =>
              (element.ownerName
                      ?.toLowerCase()
                      .contains(searchController.text.toLowerCase()) ??
                  false) ||
              (element.contactNumber
                      ?.toLowerCase()
                      .contains(searchController.text.toLowerCase()) ??
                  false))
          .toList();
      final lastPageRange = filteredList.length % 10;
      final noofPages = (filteredList.length / 20).ceil();
      print(noofPages);
      List<FranchiseDetails> subList = filteredList.isEmpty
          ? filteredList
          : filteredList
              .getRange(
                  (_currentPage * 20),
                  _currentPage == (noofPages - 1)
                      ? filteredList.length
                      // (_currentPage * 2) + lastPageRange
                      : (_currentPage + 1) * 20)
              .toList();

      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: "Franchises",
                onPressed: ctrl.addFranchise
                    ? () {
                        addfranchiseform(context, true, null);
                      }
                    : () {
                        paymentAlertDialog(context);
                      },
                button: true,
                buttonName: "ADD FRANCHISE",
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SearchField(
                    searchController: searchController,
                    onChanged: (value) {
                      setState(() {
                        _currentPage = 0;
                      });
                    },
                  ),
                  Text(
                    "Total Franchises : ${ctrl.franchises.length}",
                    style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 25),
              const FranchisesTableHeader(),
              FranchiseTile(
                franchise: subList,
                currentPage: _currentPage,
              ),
              const SizedBox(height: 20),
              filteredList.isEmpty
                  ? const SizedBox()
                  : filteredList.length > 20
                      ? NumberPaginator(
                          onPageChange: (p0) {
                            _currentPage = p0;
                            setState(() {});
                          },
                          prevButtonBuilder: (context) {
                            return filteredList.length > 20
                                ? IconButton(
                                    onPressed: () {
                                      if (_currentPage > 0) {
                                        // _currentPage = _currentPage + 1;
                                        paginatorController.prev();
                                      }
                                      setState(() {});
                                    },
                                    icon:
                                        const Icon(CupertinoIcons.chevron_back))
                                : const SizedBox();
                          },

                          nextButtonBuilder: (context) {
                            return filteredList.length > 20
                                ? IconButton(
                                    onPressed: () {
                                      if (_currentPage != noofPages - 1) {
                                        // _currentPage = _currentPage + 1;
                                        paginatorController.next();
                                      }
                                      setState(() {});
                                    },
                                    icon: const Icon(
                                        CupertinoIcons.chevron_forward))
                                : const SizedBox();
                          },

                          // initialPage: 1,
                          controller: paginatorController,
                          numberPages: noofPages,
                          // onPageChange: (int index) {
                          //   setState(() {
                          //     _currentPage = index;
                          //     _fetchNextPage();
                          //   });
                          // },
                        )
                      : const SizedBox()
            ],
          ),
        ),
      );
    });
  }
}

// Row(
//   // crossAxisAlignment: CrossAxisAlignment.end,
//   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   children: [
//     const Padding(
//       padding: EdgeInsets.only(left: 25),
//       child: Text(
//         "Franchises",
//         style: TextStyle(
//             fontSize: 25,
//             fontWeight: FontWeight.w700,
//             color: Colors.red),
//       ),
//     ),
//     Padding(
//       padding: const EdgeInsets.all(20.0),
//       child: ElevatedButton(
//           style: ButtonStyle(
//               backgroundColor: const WidgetStatePropertyAll(Colors.red),
//               padding: const WidgetStatePropertyAll(
//                   EdgeInsets.symmetric(horizontal: 20, vertical: 20)),
//               side: const WidgetStatePropertyAll(
//                   BorderSide(color: Colors.transparent)),
//               shape: WidgetStatePropertyAll(RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(5),
//               ))),
//           onPressed: () {
//             addfranchiseform(context);
//           },
//           child: const Text("ADD FRANCHISE",
//               style: TextStyle(color: Colors.white, fontSize: 16))),
//     ),
//   ],
// )
