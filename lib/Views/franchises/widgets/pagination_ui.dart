import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PaginationArrowsExpiry extends StatelessWidget {
  final bool previousBlocked;
  final bool nextsBlocked;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;

  const PaginationArrowsExpiry({
    super.key,
    required this.nextsBlocked,
    required this.previousBlocked,
    this.onPrevious,
    this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    Widget arrowButton({
      required IconData icon,
      required VoidCallback? onTap,
      bool disabled = false,
    }) {
      return GestureDetector(
        onTap: disabled ? null : onTap,
        child: Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: disabled ? Colors.grey.shade300 : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              if (!disabled)
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
            ],
            border: Border.all(
              color: disabled ? Colors.grey.shade400 : Colors.black12,
            ),
          ),
          child: Icon(
            icon,
            color: disabled ? Colors.grey : Colors.black,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        arrowButton(
          icon: Icons.chevron_left,
          onTap: onPrevious,
          disabled: previousBlocked,
        ),
        SizedBox(
          width: 20,
        ),
        SizedBox(
          width: 20,
        ),
        arrowButton(
          icon: Icons.chevron_right,
          onTap: onNext,
          disabled: nextsBlocked,
        ),
      ],
    );
  }
}
