import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/models/franchise_details.dart';

Future<dynamic> onclickedform(
    BuildContext context, bool addNew, FranchiseDetails? franchaiseData) {
  TextEditingController addresscontroller = TextEditingController();
  TextEditingController numbercontroller = TextEditingController();
  TextEditingController emailcontroller = TextEditingController();
  TextEditingController garagenamecontroller = TextEditingController();
  TextEditingController ownernamecontroller = TextEditingController();

  assignData() {
    if (!addNew) {
      ownernamecontroller.text = franchaiseData?.ownerName ?? "";
      garagenamecontroller.text = franchaiseData?.garageName ?? "";
      numbercontroller.text = franchaiseData?.contactNumber ?? "";
      emailcontroller.text = franchaiseData?.email ?? "";
      addresscontroller.text = franchaiseData?.address ?? "";
    }
  }

  return showDialog(
      context: context,
      builder: (context) {
        assignData();
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Franchise Details"),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: const Icon(Icons.close),
                )
              ],
            ),
            content: SizedBox(
                width: 800,
                height: 500,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: ownernamecontroller,
                      decoration: const InputDecoration(
                          disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Owner Name',
                          labelStyle: TextStyle(color: Colors.black)),
                    ),
                    TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: garagenamecontroller,
                      decoration: const InputDecoration(
                          labelStyle: TextStyle(color: Colors.black),
                          disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Workshop Name'),
                    ),
                    TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      // validator: (value) {
                      //   String pattern = r'^(?:[+0]9)?[0-9]{10}$';
                      //   RegExp regExp = RegExp(pattern);
                      //   if (value!.isEmpty) {
                      //     return 'Please enter a phone number';
                      //   } else if (!regExp.hasMatch(value)) {
                      //     return 'Please enter a valid phone number';
                      //   }
                      //   return null;
                      // },
                      keyboardType: TextInputType.number,
                      controller: numbercontroller,
                      decoration: const InputDecoration(
                          labelStyle: TextStyle(color: Colors.black),
                          disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          prefix: Text("+91 "),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Contact Number'),
                    ),
                    TextFormField(
                      enabled: false,
                      style: const TextStyle(color: Colors.black),
                      controller: emailcontroller,
                      decoration: const InputDecoration(
                          labelStyle: TextStyle(color: Colors.black),
                          disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Email'),
                    ),
                    TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: addresscontroller,
                      scrollPadding: const EdgeInsets.all(20),
                      decoration: const InputDecoration(
                          labelStyle: TextStyle(color: Colors.black),
                          disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          contentPadding: EdgeInsets.all(10),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Address'),
                    ),
                    // onSubmitLoad
                    //     ? const Center(
                    //         child: CircularProgressIndicator(
                    //             color: Colors.red, strokeWidth: 3.5))
                    //     : ElevatedButton(
                    //         style: ButtonStyle(
                    //             backgroundColor:
                    //                 const WidgetStatePropertyAll(Colors.red),
                    //             padding: const WidgetStatePropertyAll(
                    //                 EdgeInsets.symmetric(
                    //                     horizontal: 20, vertical: 20)),
                    //             side: const WidgetStatePropertyAll(
                    //                 BorderSide(color: Colors.transparent)),
                    //             shape: WidgetStatePropertyAll(
                    //                 RoundedRectangleBorder(
                    //               borderRadius: BorderRadius.circular(5),
                    //             ))),
                    //         onPressed: () async {
                    //           if (ownernamecontroller.text.isEmpty) {
                    //             return;
                    //           }
                    //           onSubmitLoad = true;
                    //           setState2(() {});
                    //           try {
                    //             final data = <String, dynamic>{
                    //               "ownerName": ownernamecontroller.text,
                    //               "garageName": garagenamecontroller.text,
                    //               "email": emailcontroller.text,
                    //               "address": addresscontroller.text,
                    //               "contactNumber":
                    //                   phoneNo(numbercontroller.text),
                    //               "notavailable": true,
                    //             };
                    //             if (addNew) {
                    //               final result = await FBFunctions.ff
                    //                   .httpsCallable('createFranchise')
                    //                   .call(data);
                    //               print('ress $result');
                    //               print('ress ${result.data}');
                    //             }
                    //             if (!addNew) {
                    //               FBFireStore.franchises
                    //                   .doc(franchaiseData?.docID)
                    //                   .update({
                    //                 'address': addresscontroller.text,
                    //                 'contactNumber': numbercontroller.text,
                    //                 'email': emailcontroller.text,
                    //                 'garageName': garagenamecontroller.text,
                    //                 'ownerName': ownernamecontroller.text,
                    //                 'notavailable':
                    //                     franchaiseData?.notavailable,
                    //                 // 'createdAt': DateTime.now(),
                    //               });
                    //             }
                    //             if (context.mounted) {
                    //               onSubmitLoad = false;
                    //               setState2(() {});
                    //               Navigator.pop(context);
                    //             }
                    //           } on FirebaseException catch (e) {
                    //             onSubmitLoad = false;
                    //             setState2(() {});
                    //             debugPrint(e.toString());
                    //           } catch (e) {
                    //             onSubmitLoad = false;
                    //             setState2(() {});
                    //             debugPrint(e.toString());
                    //           }
                    //           /*  onSubmitLoad = true;
                    //           setState2(() {});
                    //           try {
                    //             if (context.mounted) {
                    //               addNew
                    //                   ? FBFireStore.franchises.add({
                    //                       'address': addresscontroller.text,
                    //                       'contactNumber':
                    //                           numbercontroller.text,
                    //                       'email': emailcontroller.text,
                    //                       'garageName':
                    //                           garagenamecontroller.text,
                    //                       'ownerName': ownernamecontroller.text,
                    //                       'notavailable': false,
                    //                       'createdAt': DateTime.now(),
                    //                     })
                    //                   : FBFireStore.franchises
                    //                       .doc(franchaiseData?.docID)
                    //                       .update({
                    //                       'address': addresscontroller.text,
                    //                       'contactNumber':
                    //                           numbercontroller.text,
                    //                       'email': emailcontroller.text,
                    //                       'garageName':
                    //                           garagenamecontroller.text,
                    //                       'ownerName': ownernamecontroller.text,
                    //                       'notavailable':
                    //                           franchaiseData?.notavailable,
                    //                       // 'createdAt': DateTime.now(),
                    //                     });
                    //               Navigator.pop(context);
                    //               onSubmitLoad = true;
                    //               setState2(() {});
                    //             }
                    //           } on FirebaseException catch (e) {
                    //             debugPrint(e.toString());
                    //             onSubmitLoad = true;
                    //             setState2(() {});
                    //           } catch (e) {
                    //             debugPrint(e.toString());
                    //             onSubmitLoad = false;
                    //             setState2(() {});
                    //           } */
                    //         },
                    //         child: const Text("Submit",
                    //             style: TextStyle(
                    //                 color: Colors.white, fontSize: 16)))
                  ],
                )),
            actionsAlignment: MainAxisAlignment.start,
          );
        });
      });
}
