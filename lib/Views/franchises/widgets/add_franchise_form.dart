import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import '../../../shared/methods.dart';

Future<dynamic> addfranchiseform(
    BuildContext context, bool addNew, FranchiseDetails? franchaiseData) {
  TextEditingController addresscontroller = TextEditingController();
  TextEditingController numbercontroller = TextEditingController();
  TextEditingController emailcontroller = TextEditingController();
  TextEditingController garagenamecontroller = TextEditingController();
  TextEditingController ownernamecontroller = TextEditingController();
  bool onSubmitLoad = false;

  assignData() {
    if (!addNew) {
      ownernamecontroller.text = franchaiseData?.ownerName ?? "";
      garagenamecontroller.text = franchaiseData?.garageName ?? "";
      numbercontroller.text = franchaiseData?.contactNumber ?? "";
      emailcontroller.text = franchaiseData?.email ?? "";
      addresscontroller.text = franchaiseData?.address ?? "";
    }
  }

  return showDialog(
      context: context,
      builder: (context) {
        assignData();
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Add Franchise Details"),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: const Icon(Icons.close),
                )
              ],
            ),
            content: SizedBox(
                width: 800,
                height: 500,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextFormField(
                      controller: ownernamecontroller,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Owner Name'),
                    ),
                    TextFormField(
                      controller: garagenamecontroller,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Workshop Name'),
                    ),
                    TextFormField(
                      // validator: (value) {
                      //   String pattern = r'^(?:[+0]9)?[0-9]{10}$';
                      //   RegExp regExp = RegExp(pattern);
                      //   if (value!.isEmpty) {
                      //     return 'Please enter a phone number';
                      //   } else if (!regExp.hasMatch(value)) {
                      //     return 'Please enter a valid phone number';
                      //   }
                      //   return null;
                      // },
                      keyboardType: TextInputType.number,

                      controller: numbercontroller,
                      decoration: InputDecoration(
                          prefix: addNew ? const Text("+91 ") : const Text(""),
                          border: const OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Contact Number'),
                    ),
                    // : TextFormField(
                    //     style: TextStyle(color: Colors.grey.shade500),
                    //     enabled: false,
                    //     keyboardType: TextInputType.number,
                    //     controller: numbercontroller,
                    //     decoration: const InputDecoration(
                    //         border: OutlineInputBorder(
                    //             borderSide: BorderSide(color: Colors.black),
                    //             borderRadius:
                    //                 BorderRadius.all(Radius.circular(10))),
                    //         labelText: 'Contact Number'),
                    //   ),
                    TextFormField(
                      controller: emailcontroller,
                      enabled: addNew,
                      decoration: const InputDecoration(
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Email'),
                    ),
                    TextFormField(
                      controller: addresscontroller,
                      scrollPadding: const EdgeInsets.all(20),
                      decoration: const InputDecoration(
                          contentPadding: EdgeInsets.all(10),
                          border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          labelText: 'Address'),
                    ),
                    onSubmitLoad
                        ? const Center(
                            child: CircularProgressIndicator(
                                color: Colors.red, strokeWidth: 3.5))
                        : ElevatedButton(
                            style: ButtonStyle(
                                backgroundColor:
                                    const WidgetStatePropertyAll(Colors.red),
                                padding: const WidgetStatePropertyAll(
                                    EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 20)),
                                side: const WidgetStatePropertyAll(
                                    BorderSide(color: Colors.transparent)),
                                shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ))),
                            onPressed: () async {
                              if (ownernamecontroller.text.isEmpty) {
                                showAppSnackbar("Owner Name Empty");
                                return;
                              }
                              final phone =
                                  phoneNo(numbercontroller.text.trim());
                              if (phone.length != 13 &&
                                  !(numbercontroller.text.isNumericOnly)) {
                                showAppSnackbar("Wrong Contact Number");
                              }
                              onSubmitLoad = true;
                              setState2(() {});
                              try {
                                final data = <String, dynamic>{
                                  "ownerName": ownernamecontroller.text,
                                  "garageName": garagenamecontroller.text,
                                  "email":
                                      emailcontroller.text.trim().toLowerCase(),
                                  "address": addresscontroller.text,
                                  "contactNumber":
                                      phoneNo(numbercontroller.text),
                                  "notavailable": true,
                                  'password': getRandomId(8)
                                };
                                bool? success;
                                if (addNew) {
                                  final result = await FBFunctions.ff
                                      .httpsCallable('createFranchise')
                                      .call(data);
                                  print('ress $result');
                                  print('ress ${result.data['msg']}');
                                  // sho
                                  success = result.data['success'] == true;
                                  if (result.data['code'] ==
                                      'auth/phone-number-already-exists') {
                                    showAppSnackbar("Phone no. already exist!");
                                  } else {
                                    showAppSnackbar(result.data['msg']);
                                  }
                                }
                                if (!addNew) {
                                  try {
                                    await FBFireStore.franchises
                                        .doc(franchaiseData?.docID)
                                        .update({
                                      'address': addresscontroller.text,
                                      'contactNumber': numbercontroller.text,
                                      'email': emailcontroller.text
                                          .trim()
                                          .toLowerCase(),
                                      'garageName': garagenamecontroller.text,
                                      'ownerName': ownernamecontroller.text,
                                      'notavailable':
                                          franchaiseData?.notavailable,
                                      // 'createdAt': DateTime.now(),
                                    });
                                    success = true;
                                  } on Exception catch (e) {
                                    showAppSnackbar(e.toString());
                                    onSubmitLoad = false;
                                    setState2(() {});
                                    success = false;
                                  }
                                }
                                if (context.mounted) {
                                  onSubmitLoad = false;
                                  setState2(() {});

                                  if (success == true) {
                                    Navigator.pop(context);
                                  }
                                }
                              } on FirebaseException catch (e) {
                                onSubmitLoad = false;
                                setState2(() {});
                                debugPrint(e.toString());
                              } catch (e) {
                                onSubmitLoad = false;
                                setState2(() {});
                                debugPrint(e.toString());
                              }
                              /*  onSubmitLoad = true;
                              setState2(() {});
                              try {
                                if (context.mounted) {
                                  addNew
                                      ? FBFireStore.franchises.add({
                                          'address': addresscontroller.text,
                                          'contactNumber':
                                              numbercontroller.text,
                                          'email': emailcontroller.text,
                                          'garageName':
                                              garagenamecontroller.text,
                                          'ownerName': ownernamecontroller.text,
                                          'notavailable': false,
                                          'createdAt': DateTime.now(),
                                        })
                                      : FBFireStore.franchises
                                          .doc(franchaiseData?.docID)
                                          .update({
                                          'address': addresscontroller.text,
                                          'contactNumber':
                                              numbercontroller.text,
                                          'email': emailcontroller.text,
                                          'garageName':
                                              garagenamecontroller.text,
                                          'ownerName': ownernamecontroller.text,
                                          'notavailable':
                                              franchaiseData?.notavailable,
                                          // 'createdAt': DateTime.now(),
                                        });
                                  Navigator.pop(context);
                                  onSubmitLoad = true;
                                  setState2(() {});
                                }
                              } on FirebaseException catch (e) {
                                debugPrint(e.toString());
                                onSubmitLoad = true;
                                setState2(() {});
                              } catch (e) {
                                debugPrint(e.toString());
                                onSubmitLoad = false;
                                setState2(() {});
                              } */
                            },
                            child: const Text("Submit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16)))
                  ],
                )),
            actionsAlignment: MainAxisAlignment.start,
          );
        });
      });
}
