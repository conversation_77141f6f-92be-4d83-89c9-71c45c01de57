import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/get_core.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:intl/intl.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/franchises/widgets/add_franchise_form.dart';
import 'package:speed_force_admin/Views/franchises/widgets/onclicked_form.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/methods.dart';

class FranchiseTile extends StatefulWidget {
  const FranchiseTile({
    super.key,
    required this.franchise,
    required this.currentPage,
  });
  final List<FranchiseDetails> franchise;
  final int currentPage;
  @override
  State<FranchiseTile> createState() => _FranchiseTileState();
}

class _FranchiseTileState extends State<FranchiseTile> {
  // List<FranchiseDetails> franchises = [];
  // paginatedata() {
  //   final first = FirebaseFirestore.instance
  //       .collection('Franchises')
  //       .orderBy('createdAt')
  //       .limit(10);

  //   first.get().then(
  //     (documentSnapshots) {
  //       // Get the last visible document

  //       franchises.addAll(documentSnapshots.docs.map(
  //         (e) => FranchiseDetails.fromSnap(e),
  //       ));
  //       setState(() {});
  //       final lastVisible = documentSnapshots.docs[documentSnapshots.size - 1];

  //       // Construct a new query starting at this document,
  //       // get the next 25 cities.
  //       final next = FirebaseFirestore.instance
  //           .collection("Franchises")
  //           .orderBy("createdAt")
  //           .startAfterDocument(lastVisible)
  //           .limit(10);

  //       // Use the query for pagination
  //       // ...
  //     },
  //     onError: (e) => print("Error completing: $e"),
  //   );
  // }

  // @override
  // void initState() {
  //   // TODO: implement initState
  //   super.initState();
  //   paginatedata();
  // }

  @override
  Widget build(BuildContext context) {
    bool loading = false;
    // print(franchises.length);
    // return GetBuilder<HomeCtrl>(builder: (_) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.franchise.length,
      itemBuilder: (context, index) {
        DateTime createdAt = widget.franchise[index].createdAt.toDate();
        String formattedDate = DateFormat('dd/MM/yyyy').format(createdAt);
        return InkWell(
          overlayColor: const WidgetStatePropertyAll(Colors.transparent),
          onTap: () {
            onclickedform(context, false, widget.franchise[index]);
            // addfranchiseform(context, false, widget.franchise[index]);
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: index % 2 != 0
                ? BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[200])
                : null,
            child: Row(
              children: [
                SizedBox(
                    width: 80,
                    child: Text(
                        ((index + 1) + (20 * widget.currentPage)).toString(),
                        textAlign: TextAlign.center)),
                SizedBox(
                    width: 100,
                    child: Text(overflow: TextOverflow.ellipsis, formattedDate
                        // widget.franchise[index].createdAt.toDate().toString()
                        )),
                const SizedBox(width: 5),
                SizedBox(
                    width: 100,
                    child: Text(
                        overflow: TextOverflow.ellipsis,
                        widget.franchise[index].ownerName?.toLowerCase() ??
                            "")),
                const SizedBox(width: 30),
                SizedBox(
                    width: 160,
                    child: Text(
                        overflow: TextOverflow.ellipsis,
                        widget.franchise[index].garageName ?? "")),
                const SizedBox(width: 22),
                SizedBox(
                    width: 160,
                    child: Text(widget.franchise[index].contactNumber ?? "")),
                const SizedBox(width: 5),
                SizedBox(
                    width: 160,
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      widget.franchise[index].email ?? "",
                      maxLines: 1,
                    )),
                const SizedBox(width: 5),
                Expanded(
                  child: Wrap(
                    alignment: WrapAlignment.end,
                    children: [
                      IconButton(
                          onPressed: Get.find<HomeCtrl>().addFranchise
                              ? () {
                                  addfranchiseform(
                                      context, false, widget.franchise[index]);
                                }
                              : () {
                                  paymentAlertDialog(context);
                                },
                          icon: const Icon(Icons.edit)),
                      Transform.scale(
                        scale: .7,
                        child: CupertinoSwitch(
                          value:
                              !(widget.franchise[index].notavailable ?? false),
                          onChanged: (valueeee) {
                            if (Get.find<HomeCtrl>().addFranchise) {
                              FBFireStore.franchises
                                  .doc(widget.franchise[index].docID)
                                  .update({'notavailable': !valueeee});
                            } else {
                              paymentAlertDialog(context);
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                    onPressed: !Get.find<HomeCtrl>().addFranchise
                        ? () {
                            paymentAlertDialog(context);
                          }
                        : () => showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return StatefulBuilder(
                                    builder: (context, setState2) {
                                  return AlertDialog(
                                    title: const Text("Alert"),
                                    content: const Text(
                                        "Are you sure you want to delete"),
                                    actions: loading
                                        ? [
                                            const Center(
                                              child: SizedBox(
                                                height: 25,
                                                width: 25,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2.5,
                                                ),
                                              ),
                                            )
                                          ]
                                        : [
                                            TextButton(
                                                onPressed: () async {
                                                  // Navigator.of(context).pop();
                                                  try {
                                                    setState2(() {
                                                      loading = true;
                                                    });
                                                    final data =
                                                        <String, dynamic>{
                                                      "uid": widget
                                                          .franchise[index]
                                                          .docID
                                                    };

                                                    final result =
                                                        await FBFunctions
                                                            .ff
                                                            .httpsCallable(
                                                                'deleteFranchise')
                                                            .call(data);
                                                    print(
                                                        result.data["success"]);

                                                    const snackBar = SnackBar(
                                                        content: Text("Data"));
                                                    if (context.mounted) {
                                                      Navigator.of(context)
                                                          .pop();
                                                      setState2(() {
                                                        loading = false;
                                                      });
                                                      ScaffoldMessenger.of(
                                                              context)
                                                          .showSnackBar(
                                                              snackBar);
                                                    }
                                                  } catch (e) {
                                                    debugPrint(e.toString());

                                                    if (context.mounted) {
                                                      setState2(() {
                                                        loading = false;
                                                      });
                                                      Navigator.of(context)
                                                          .pop();
                                                    }
                                                  }
                                                },
                                                child: const Text('Yes')),
                                            TextButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text('No')),
                                          ],
                                  );
                                });
                              },
                            ),
                    icon: const Icon(Icons.delete)),
                // paginatedata(),
              ],
            ),
          ),
        );
      },
    );
    // });
  }
}
