import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/get_core.dart';
import 'package:get/get_instance/get_instance.dart';
import 'package:intl/intl.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/franchises/widgets/add_franchise_form.dart';
import 'package:speed_force_admin/Views/franchises/widgets/onclicked_form.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/methods.dart';

class FranchiseTile extends StatefulWidget {
  const FranchiseTile({
    super.key,
    required this.franchise,
    required this.currentPage,
  });
  final List<FranchiseDetails> franchise;
  final int currentPage;
  @override
  State<FranchiseTile> createState() => _FranchiseTileState();
}

class _FranchiseTileState extends State<FranchiseTile> {
  @override
  Widget build(BuildContext context) {
    bool loading = false;

    return ListView.builder(
      shrinkWrap: true,
      itemCount: widget.franchise.length,
      itemBuilder: (context, index) {
        return FranchiseDetailsTile(
          index: index,
          franchise: widget.franchise[index],
          currentPage: widget.currentPage,
        );
      },
    );
    // });
  }
}

class FranchiseDetailsTile extends StatefulWidget {
  const FranchiseDetailsTile(
      {super.key,
      required this.franchise,
      required this.index,
      required this.currentPage});
  final FranchiseDetails franchise;
  final int index;
  final int currentPage;
  @override
  State<FranchiseDetailsTile> createState() => _FranchiseDetailsTileState();
}

class _FranchiseDetailsTileState extends State<FranchiseDetailsTile> {
  int? totalCustomers;
  int? totalVehicles;
  bool loading = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getCount();
  }

  getCount() async {
    loading = true;
    if (mounted) {
      setState(() {});
    }
    totalCustomers = (await FBFireStore.customers
            .where('franchiseId', isEqualTo: widget.franchise.docID)
            .count()
            .get())
        .count;
    totalVehicles = (await FBFireStore.customersvehicles
            .where('franchiseId', isEqualTo: widget.franchise.docID)
            .count()
            .get())
        .count;
    loading = false;
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime createdAt = widget.franchise.createdAt.toDate();
    String formattedDate = DateFormat('dd/MM/yyyy').format(createdAt);
    return InkWell(
      overlayColor: const WidgetStatePropertyAll(Colors.transparent),
      onTap: () {
        onclickedform(context, false, widget.franchise);
        // addfranchiseform(context, false, widget.franchise[index]);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: widget.index % 2 != 0
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(4), color: Colors.grey[200])
            : null,
        child: Row(
          children: [
            SizedBox(
                width: 80,
                child: Text(
                    ((widget.index + 1) + (20 * widget.currentPage)).toString(),
                    textAlign: TextAlign.center)),
            SizedBox(
                width: 100,
                child: Text(overflow: TextOverflow.ellipsis, formattedDate
                    // widget.franchise[index].createdAt.toDate().toString()
                    )),
            const SizedBox(width: 5),
            SizedBox(
                width: 100,
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    widget.franchise.ownerName?.toLowerCase() ?? "")),
            const SizedBox(width: 30),
            SizedBox(
                width: 160,
                child: Text(
                    overflow: TextOverflow.ellipsis,
                    widget.franchise.garageName ?? "")),
            const SizedBox(width: 22),
            SizedBox(
                width: 160, child: Text(widget.franchise.contactNumber ?? "")),
            const SizedBox(width: 5),
            SizedBox(
                width: 160,
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  widget.franchise.email ?? "",
                  maxLines: 1,
                )),
            const SizedBox(width: 5),
            SizedBox(
                width: 160,
                child: loading
                    ? const Center(
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                          ),
                        ),
                      )
                    : Center(
                        child: Text(
                          overflow: TextOverflow.ellipsis,
                          totalCustomers?.toString() ?? "",
                          maxLines: 1,
                        ),
                      )),
            const SizedBox(width: 5),
            SizedBox(
                width: 160,
                child: loading
                    ? const Center(
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                          ),
                        ),
                      )
                    : Center(
                        child: Text(
                          overflow: TextOverflow.ellipsis,
                          totalVehicles?.toString() ?? "",
                          maxLines: 1,
                        ),
                      )),
            const SizedBox(width: 5),
            Expanded(
              child: Wrap(
                alignment: WrapAlignment.end,
                children: [
                  IconButton(
                      onPressed: Get.find<HomeCtrl>().addFranchise
                          ? () {
                              addfranchiseform(
                                  context, false, widget.franchise);
                            }
                          : () {
                              paymentAlertDialog(context);
                            },
                      icon: const Icon(Icons.edit)),
                  Transform.scale(
                    scale: .7,
                    child: CupertinoSwitch(
                      value: !(widget.franchise.notavailable ?? false),
                      onChanged: (valueeee) {
                        if (Get.find<HomeCtrl>().addFranchise) {
                          FBFireStore.franchises
                              .doc(widget.franchise.docID)
                              .update({'notavailable': !valueeee});
                        } else {
                          paymentAlertDialog(context);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
                onPressed: !Get.find<HomeCtrl>().addFranchise
                    ? () {
                        paymentAlertDialog(context);
                      }
                    : () => showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return StatefulBuilder(
                                builder: (context, setState2) {
                              return AlertDialog(
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              // Navigator.of(context).pop();
                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                final data = <String, dynamic>{
                                                  "uid": widget.franchise.docID
                                                };

                                                final result = await FBFunctions
                                                    .ff
                                                    .httpsCallable(
                                                        'deleteFranchise')
                                                    .call(data);
                                                print(result.data["success"]);

                                                const snackBar = SnackBar(
                                                    content: Text("Data"));
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            });
                          },
                        ),
                icon: const Icon(Icons.delete)),
            // paginatedata(),
          ],
        ),
      ),
    );
  }
}
