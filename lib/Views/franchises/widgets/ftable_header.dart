import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';
import '../../../shared/theme.dart';

class FranchisesTableHeader extends StatelessWidget {
  const FranchisesTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 228, 228, 228),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(
              width: 70,
              child: Text('Sr No',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontWeight: FontWeight.w600))),
          const SizedBox(width: 20),
          const SizedBox(
              width: 90, child: TableHeaderText(headerName: 'Registered')),
          const SizedBox(width: 20),
          const SizedBox(
              width: 90, child: TableHeaderText(headerName: 'Owner Name')),
          const SizedBox(width: 30),
          const SizedBox(
              width: 160, child: TableHeaderText(headerName: 'Workshop Name')),
          const SizedBox(width: 30),
          const SizedBox(
              width: 160, child: TableHeaderText(headerName: 'Contact No.')),
          const SizedBox(width: 10),
          const SizedBox(
              width: 160, child: TableHeaderText(headerName: 'Email')),
          Opacity(
            opacity: 0,
            child: SizedBox(
                width: 60,
                child: Transform.scale(
                  scale: .65,
                  child: CupertinoSwitch(
                    value: true,
                    onChanged: (value) {},
                  ),
                )),
          ),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  CupertinoIcons.star,
                  size: 22,
                  color: Colors.amber,
                ),
              ),
            ),
          ),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.delete,
                  color: themeColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

          // const SizedBox(width: 20),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Contact')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Category')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'City')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150,
          //     child: TableHeaderText(headerName: 'Sponsored Priority')),
          // const SizedBox(width: 5),

          // const TableHeaderText(headerName: 'City'),
          // const SizedBox(width: 5),

          // const TableHeaderText(headerName: 'Price'),
          // const SizedBox(width: 5),

          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         CupertinoIcons.pencil,
          //         size: 22,
          //       ),
          //     ),
          //   ),
          // ),