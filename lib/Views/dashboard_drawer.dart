import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/drawer_logout.dart';
import 'package:speed_force_admin/Views/drawer_tile.dart';
import 'package:speed_force_admin/shared/router.dart';

class DashboardDrawer extends StatelessWidget {
  final GlobalKey<ScaffoldState>? scafKey;
  const DashboardDrawer({super.key, this.scafKey});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (hctrl) {
      return SizedBox(
        width: 280,
        child: Column(
          children: [
            const SizedBox(height: 30),
            const SpeedForceLogo(),
            const SizedBox(height: 40),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DashboardTile(
                      icon: Icons.house_outlined,
                      textName: 'Franchises',
                      scafKey: scafKey,
                      route: Routes.franchises,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.person_3,
                      textName: 'Customers',
                      scafKey: scafKey,
                      route: Routes.customers,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.hammer,
                      textName: 'Services',
                      scafKey: scafKey,
                      route: Routes.services,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.add,
                      textName: 'Parts',
                      scafKey: scafKey,
                      route: Routes.parts,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.checkmark_square,
                      textName: 'Make/Create Model',
                      scafKey: scafKey,
                      route: Routes.makeCreate,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: Icons.file_copy_outlined,
                      textName: 'Report',
                      scafKey: scafKey,
                      route: Routes.report,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.car_detailed,
                      textName: 'Vehicles',
                      scafKey: scafKey,
                      route: Routes.vehicles,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.rectangle_dock,
                      textName: 'Banners',
                      scafKey: scafKey,
                      route: Routes.banners,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.bell,
                      textName: 'Notifications',
                      scafKey: scafKey,
                      route: Routes.notifications,
                    ),
                    const SizedBox(height: 8),
                    DashboardTile(
                      icon: CupertinoIcons.settings,
                      textName: 'Settings',
                      scafKey: scafKey,
                      route: Routes.settings,
                    ),
                    // const Spacer(),
                  ],
                ),
              ),
            ),
            // const Spacer(),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              child: Center(child: DrawerLogout()),
            ),
          ],
        ),
      );
    });
  }
}

class SpeedForceLogo extends StatelessWidget {
  const SpeedForceLogo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      height: 100,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
      child: Row(
        children: [
          Expanded(
            child: Image.asset(
              'assets/logo.png',
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
