import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/shared/router.dart';

class DashboardTile extends StatefulWidget {
  const DashboardTile(
      {super.key,
      required this.icon,
      required this.textName,
      this.scafKey,
      required this.route});

  final IconData icon;
  final String textName;
  final GlobalKey<ScaffoldState>? scafKey;
  final String route;

  @override
  State<DashboardTile> createState() => _DashboardTileState();
}

class _DashboardTileState extends State<DashboardTile> {
  @override
  Widget build(BuildContext context) {
    // print(FBAuth.auth.currentUser);
    final selected =
        appRouter.routeInformationProvider.value.uri.path == widget.route;

    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: () {
        context.go(widget.route);
        // setState(() {});
        // scafKey?.currentState?.closeDrawer();
      },
      child: Container(
        decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: selected ? Colors.red : null,
            borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.all(10),
        width: 250,
        child: Row(
          children: [
            _row(selected),
          ],
        ),
      ),
    );
  }

  Row _row(bool selected) {
    return Row(
      children: [
        _icon(selected),
        const SizedBox(width: 15),
        _text(selected),
      ],
    );
  }

  Text _text(bool selected) {
    return Text(widget.textName,
        textAlign: TextAlign.center,
        style: selected
            ? TextStyle(
                letterSpacing: 1,
                fontWeight: selected ? FontWeight.w500 : FontWeight.w400,
                color: Colors.white)
            : null);
  }

  Icon _icon(bool selected) =>
      Icon(widget.icon, color: selected ? Colors.white : null);
}
