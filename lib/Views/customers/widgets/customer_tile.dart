import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/customers/widgets/repair_order_invoice_data.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/methods.dart';

class CustomersTile extends StatefulWidget {
  const CustomersTile({
    super.key,
    required this.customers,
  });
  final List<CustomersModel> customers;

  @override
  State<CustomersTile> createState() => _CustomersTileState();
}

class _CustomersTileState extends State<CustomersTile> {
  final GlobalKey _globalKey = GlobalKey();
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return ListView.builder(
        shrinkWrap: true,
        itemCount: widget.customers.length,
        itemBuilder: (context, index) {
          final fdata = ctrl.franchises.firstWhereOrNull((element) =>
              element.docID == widget.customers[index].franchiseId);

          return InkWell(
            onTap: () async {
              final data = await FirebaseFirestore.instance
                  .collection(FirebaseCollections.orders.name)
                  .where('customerId', isEqualTo: widget.customers[index].docId)
                  .orderBy('createdAt', descending: true)
                  .limit(1)
                  .get();
              if (data.size > 0) {
                RepairOrderModel repairOrder = RepairOrderModel.fromMap(
                    data.docs.first.id, data.docs.first.data());

                final data2 = await FirebaseFirestore.instance
                    .collection(FirebaseCollections.invoices.name)
                    .where('invoiceId', isEqualTo: repairOrder.invoiceId)
                    .orderBy('createdAt', descending: true)
                    .limit(1)
                    .get();

                RepairOrderModel? invoice = RepairOrderModel.fromMap(
                    data2.docs.first.id, data2.docs.first.data());
                // await showModalBottomSheet(
                //   context: context,
                //   scrollControlDisabledMaxHeightRatio: 0.9,
                //   builder: (context) {
                //     return RepairOrderInvoiceData(
                //       franchises: fdata,
                //       globalKey: _globalKey,
                //       repairOrderModel: repairOrder,
                //       invoiceModel: invoice,
                //     );
                //   },
                // );
                await showDialog(
                  context: context,
                  builder: (context) => Dialog(
                    backgroundColor: Colors.white,
                    child: Container(
                      constraints: BoxConstraints(maxWidth: 600),
                      child: RepairOrderInvoiceData(
                        franchises: fdata,
                        globalKey: _globalKey,
                        repairOrderModel: repairOrder,
                        invoiceModel: invoice,
                      ),
                    ),
                  ),
                );
              } else {
                showAppSnackbar("No Bills to display");
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: index % 2 != 0
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.grey[200])
                  : null,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // SizedBox(
                  //     width: 70,
                  //     child: Text((index + 1).toString(),
                  //         textAlign: TextAlign.center)),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //         overflow: TextOverflow.ellipsis,
                  //         widget.customers[index].username ?? "".toLowerCase())),
                  // const SizedBox(width: 5),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //         overflow: TextOverflow.ellipsis,
                  //         fdata?.garageName ?? "")),
                  // const SizedBox(width: 5),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //         overflow: TextOverflow.ellipsis,
                  //         widget.customers[index].phone ?? "")),
                  // const SizedBox(width: 5),
                  // // SizedBox(
                  // //     width: 150,
                  // //     child: Text(
                  // //       overflow: TextOverflow.ellipsis,
                  // //       widget.customers[index].email ?? "",
                  // //       maxLines: 1,
                  // //     )),
                  // // const SizedBox(width: 5),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //       overflow: TextOverflow.ellipsis,
                  //       widget.customers[index].email ?? "",
                  //       maxLines: 1,
                  //     )),
                  // const SizedBox(width: 5),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //       overflow: TextOverflow.ellipsis,
                  //       widget.customers[index].email ?? "",
                  //       maxLines: 1,
                  //     )),
                  // const SizedBox(width: 5),
                  SizedBox(
                      width: 70,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center)),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Text(
                        overflow: TextOverflow.ellipsis,
                        widget.customers[index].username ?? "".toLowerCase()),
                  ),
                  const SizedBox(width: 60),
                  Expanded(
                    child: Text(
                        overflow: TextOverflow.ellipsis,
                        fdata?.garageName ?? ""),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                        overflow: TextOverflow.ellipsis,
                        widget.customers[index].phone ?? ""),
                  ),
                  const SizedBox(width: 10),
                  // SizedBox(
                  //     width: 150,
                  //     child: Text(
                  //       overflow: TextOverflow.ellipsis,
                  //       widget.customers[index].email ?? "",
                  //       maxLines: 1,
                  //     )),
                  // const SizedBox(width: 5),
                  Expanded(
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      widget.customers[index].lastVisit ?? "",
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      overflow: TextOverflow.ellipsis,
                      widget.customers[index].totalBillAmount.toString() == "0"
                          ? "-"
                          : widget.customers[index].totalBillAmount.toString(),
                      maxLines: 1,
                    ),
                  ),
                  // const SizedBox(width: 10),
                  // Expanded(
                  //   child: Wrap(
                  //     alignment: WrapAlignment.end,
                  //     children: [
                  //       IconButton(
                  //           onPressed: () {
                  //             addfranchiseform(
                  //                 context, false, _.franchises[index]);
                  //           },
                  //           icon: const Icon(Icons.edit)),
                  //       Transform.scale(
                  //         scale: .7,
                  //         child: CupertinoSwitch(
                  //           value: !_.franchises[index].notavailable,
                  //           onChanged: (valueeee) {
                  //             FBFireStore.franchises
                  //                 .doc(_.franchises[index].docID)
                  //                 .update({'notavailable': !valueeee});
                  //           },
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // IconButton(
                  //     onPressed: () => showDialog(
                  //           context: context,
                  //           builder: (BuildContext context) {
                  //             return AlertDialog(
                  //               actions: [
                  //                 Padding(
                  //                   padding: const EdgeInsets.all(0),
                  //                   child: Column(
                  //                     children: [
                  //                       const SizedBox(height: 25),
                  //                       const Text("Are You Sure?"),
                  //                       const SizedBox(height: 10),
                  //                       Row(
                  //                         mainAxisAlignment:
                  //                             MainAxisAlignment.center,
                  //                         children: [
                  //                           TextButton(
                  //                             onPressed: () async {
                  //                               FBFireStore.franchises
                  //                                   .doc(_.franchises[index]
                  //                                       .docID)
                  //                                   .delete();
                  //                               context.pop();
                  //                             },
                  //                             child: const Text("YES"),
                  //                           ),
                  //                           TextButton(
                  //                             onPressed: () {
                  //                               context.pop();
                  //                             },
                  //                             child: const Text("NO"),
                  //                           )
                  //                         ],
                  //                       )
                  //                     ],
                  //                   ),
                  //                 ),
                  //               ],
                  //             );
                  //           },
                  //         ),
                  //     icon: const Icon(Icons.delete)),
                ],
              ),
            ),
          );
        },
      );
    });
  }
}
