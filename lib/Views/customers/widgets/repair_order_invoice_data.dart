import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:speed_force_admin/Views/customers/widgets/order_info_container.dart';
import 'package:speed_force_admin/configurations/app_configurations.dart';
import 'package:speed_force_admin/constants/constants.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'dart:ui' as ui;

import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/const.dart';

class RepairOrderInvoiceData extends StatefulWidget {
  const RepairOrderInvoiceData(
      {super.key,
      required this.globalKey,
      required this.repairOrderModel,
      this.invoiceModel,
      required this.franchises});

  final GlobalKey globalKey;
  final RepairOrderModel repairOrderModel;
  final FranchiseDetails? franchises;
  final RepairOrderModel? invoiceModel;

  @override
  State<RepairOrderInvoiceData> createState() => _RepairOrderInvoiceDataState();
}

class _RepairOrderInvoiceDataState extends State<RepairOrderInvoiceData> {
  int _currentSelection = 0;
  double subTotal = 0;
  double tax = 0;
  bool loader = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // Get.delete<RepairOrderDetailsController>();
    // Get.put(RepairOrderDetailsController());
    final orderserviceinfoList = [
      ...List.generate(
        widget.repairOrderModel.repairDetailsModel!.services!.length,
        (index) {
          final serviceModel =
              widget.repairOrderModel.repairDetailsModel!.services![index];
          subTotal = (serviceModel.rate!) + subTotal;
          (widget.repairOrderModel.gstIncluded ?? false)
              ? tax += serviceModel.servicesGst ?? 0
              : 0;

          return (widget.repairOrderModel.gstIncluded ?? false)
              ? [
                  serviceModel.title!,
                  serviceModel.rate.toString(),
                  serviceModel.gstRate.toString(),
                  serviceModel.servicesGst.toString(),
                  ((serviceModel.amount ?? 0) +
                          (serviceModel.servicesGst?.toDouble() ?? 0))
                      .toString(),
                ]
              : [
                  serviceModel.title!,
                  serviceModel.rate.toString(),
                  serviceModel.amount.toString(),
                ];
        },
      )
    ];
    final orderPartsinfoList = [
      ...List.generate(
        widget.repairOrderModel.repairDetailsModel!.parts!.length,
        (index) {
          final partModel =
              widget.repairOrderModel.repairDetailsModel!.parts![index];
          subTotal = (partModel.rate!) + subTotal;
          (widget.repairOrderModel.gstIncluded ?? false)
              ? tax += partModel.partsGst ?? 0
              : 0;
          return (widget.repairOrderModel.gstIncluded ?? false)
              ? [
                  [
                    partModel.title!,
                    partModel.quantity!.toString(),
                  ],
                  partModel.rate.toString(),
                  partModel.gstRate.toString(),
                  partModel.partsGst.toString(),
                  ((partModel.amount ?? 0) +
                          (partModel.partsGst?.toDouble() ?? 0))
                      .toString(),
                ]
              : [
                  [
                    partModel.title!,
                    partModel.quantity!.toString(),
                  ],
                  partModel.rate.toString(),
                  partModel.amount.toString()
                ];
        },
      )
    ];
  }

  @override
  Widget build(BuildContext context) {
    // RepairOrderDetailsController repairOrderDetailsController =
    //     Get.find<RepairOrderDetailsController>();
    // HomeScreenController homeScreenController =
    //     Get.find<HomeScreenController>();

    return Container(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            HeightBox(10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CupertinoSlidingSegmentedControl(
                    children: {
                      0: Container(
                        child: Text(
                          'Repair Order',
                          style: TextStyle(
                              color: _currentSelection == 0
                                  ? Colors.white
                                  : Colors.black),
                        ),
                      ),
                      1: Container(
                        margin: EdgeInsets.symmetric(horizontal: 2),
                        child: Text(
                          'Invoice',
                          style: TextStyle(
                            color: _currentSelection == 1
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      ),
                    },
                    groupValue: _currentSelection,
                    thumbColor: Colors.redAccent,
                    onValueChanged: (index) {
                      setState(() {
                        _currentSelection = index ?? 0;
                      });
                    }),
                const Spacer(),
                InkWell(
                  onTap: loader
                      ? null
                      : () async {
                          setState(() {
                            loader = true;
                          });

                          // Small delay to allow UI update
                          await Future.delayed(Duration(milliseconds: 100));

                          try {
                            pw.Document pdf = await _capturePdf();
                            await _sharePdf(pdf);
                          } catch (e) {
                            print("Error generating PDF: $e");
                          } finally {
                            if (mounted) {
                              setState(() {
                                loader = false;
                              });
                            }
                          }
                          // pw.Document pdf = await _capturePdf();
                          // await _sharePdf(pdf);
                        },
                  child: loader
                      ? Text(
                          "...",
                          style: TextStyle(color: Colors.black54, fontSize: 24),
                        )
                      : Icon(Icons.download),
                ),
                // WidthBox(10),
                // InkWell(
                //   onTap: () async {
                //     await savePdf();
                //     if (context.mounted) {
                //       Navigator.pop(context);
                //     }
                //   },
                //   child: const Icon(Icons.download),
                // ),
                WidthBox(10),
                InkWell(
                  onTap: loader
                      ? null
                      : () {
                          Navigator.pop(context);
                        },
                  child: const Icon(Icons.close),
                ),
                WidthBox(10),
              ],
            ),
            HeightBox(10),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    RepaintBoundary(
                      key: widget.globalKey,
                      child: Column(
                        children: [
                          HeightBox(10),
                          Text(
                            _currentSelection == 0 ? "Repair Order" : "Invoice",
                            style: TextStyle(
                                fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          HeightBox(10),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Image.asset(
                                    'assets/logo.png',
                                    // height: 80,
                                    width: 100,
                                    fit: BoxFit.fill,
                                  ),
                                ),
                                WidthBox(50),
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        widget.franchises?.garageName ?? "",
                                        textAlign: TextAlign.right,
                                      ),
                                      HeightBox(5),
                                      Text(
                                        widget.franchises?.address ?? "",
                                        textAlign: TextAlign.right,
                                      ),
                                      HeightBox(5),
                                      Row(
                                        children: [
                                          const Icon(Icons.phone),
                                          WidthBox(5),
                                          Expanded(
                                            child: Text(
                                              widget.franchises
                                                      ?.contactNumber ??
                                                  "",
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          const Icon(Icons.email),
                                          WidthBox(5),
                                          Expanded(
                                            child: Text(
                                              widget.franchises?.email ?? "",
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          HeightBox(20),
                          Row(
                            children: [
                              WidthBox(5),
                              Text(
                                _currentSelection == 0
                                    ? """Delivery Time: ${DateFormat.d().add_MMM().add_y().add_jm().format(
                                          DateTime.tryParse(
                                                widget
                                                        .repairOrderModel
                                                        .repairDetailsModel
                                                        ?.estimatedDelivery ??
                                                    "",
                                              ) ??
                                              DateTime.now(),
                                        )}"""
                                    : """Job Card: ${DateFormat.d().add_MMM().add_y().add_jm().format(
                                          DateTime.tryParse(
                                                widget.invoiceModel
                                                        ?.createdAt ??
                                                    "",
                                              ) ??
                                              DateTime.now(),
                                        )}  """,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: colorsConstants.hintGrey,
                                ),
                              ),
                            ],
                          ),
                          HeightBox(10),
                          OrderInfoContainer(
                            orderInfoModels: [
                              OrderInfoModel(header: "CUSTOMER", columnData: [
                                widget.repairOrderModel.customerDetailsModel
                                        ?.username ??
                                    "",
                                widget.repairOrderModel.customerDetailsModel
                                        ?.phone ??
                                    "",
                                // widget.repairOrderModel.customerDetailsModel
                                //         ?.email ??
                                //     "",
                                // widget.repairOrderModel.customerDetailsModel
                                //         ?.address ??
                                //     "",
                              ]),
                              OrderInfoModel(
                                header: "VEHICLE",
                                columnData: [
                                  "${widget.repairOrderModel.vehicleDetailsModel?.registrationNumber ?? "-"}"
                                      "${widget.repairOrderModel.vehicleDetailsModel?.make ?? "-"} ${widget.repairOrderModel.vehicleDetailsModel?.model ?? "-"} "
                                      "",
                                  // widget.repairOrderModel.vehicleDetailsModel
                                  //         ?.model ??
                                  //     "",
                                ],
                              ),
                              OrderInfoModel(
                                header: _currentSelection == 0
                                    ? "ESTIMATE"
                                    : "INVOICE",
                                columnData: _currentSelection == 0
                                    ? [
                                        (DateFormat.d()
                                            .add_MMM()
                                            .add_y()
                                            .add_jm()
                                            .format(
                                              DateTime.parse(
                                                widget
                                                        .repairOrderModel
                                                        .repairDetailsModel
                                                        ?.estimatedDelivery ??
                                                    "",
                                              ),
                                            )),
                                        "Amount: ${Constants.rupeeSign}${widget.repairOrderModel.repairDetailsModel?.total.toString()}",
                                      ]
                                    : [
                                        """# ${widget.repairOrderModel.jobCardId}""",
                                        // DateFormat("MMM d, y").format(
                                        //   DateTime.parse(
                                        //       widget.invoiceModel!.createdAt ??
                                        //           ""),
                                        // ),
                                        "Amount: ${Constants.rupeeSign}${widget.repairOrderModel.repairDetailsModel?.total.toString()}",
                                      ],
                              ),
                            ],
                          ),
                          HeightBox(10),
                          OrderInfoContainer(
                            orderInfoModels: [
                              OrderInfoModel(
                                header: "SERVICES",
                                columnData: widget.repairOrderModel
                                        .repairDetailsModel?.services
                                        ?.map((service) {
                                      return service.title ?? "";
                                    }).toList() ??
                                    [],
                              ),
                              OrderInfoModel(
                                header: "QTY",
                                columnData: widget.repairOrderModel
                                    .repairDetailsModel!.services!
                                    .map((service) {
                                  return service.quantity.toString();
                                }).toList(),
                              ),
                              OrderInfoModel(
                                header: "RATE",
                                columnData: widget.repairOrderModel
                                    .repairDetailsModel!.services!
                                    .map((service) {
                                  return service.rate.toString();
                                }).toList(),
                              ),
                              if (widget.repairOrderModel.gstIncluded ??
                                  false) ...[
                                OrderInfoModel(
                                  header: "TAX RATE",
                                  columnData: widget.repairOrderModel
                                      .repairDetailsModel!.services!
                                      .map((service) {
                                    return service.gstRate.toString();
                                  }).toList(),
                                ),
                                OrderInfoModel(
                                  header: "TAX AMOUNT",
                                  columnData: widget.repairOrderModel
                                      .repairDetailsModel!.services!
                                      .map((service) {
                                    return service.servicesGst.toString();
                                  }).toList(),
                                ),
                              ],
                              OrderInfoModel(
                                header: "AMOUNT",
                                columnData: widget.repairOrderModel
                                    .repairDetailsModel!.services!
                                    .map((service) {
                                  return ((service.amount ?? 0) +
                                          (service.servicesGst ?? 0))
                                      .toString();
                                }).toList(),
                              ),
                            ],
                            totalTitle: "Total",
                            totalAmount:
                                "${widget.repairOrderModel.repairDetailsModel?.servicesTotal?.toStringAsFixed(2)}",
                          ),
                          HeightBox(10),
                          OrderInfoContainer(
                            orderInfoModels: [
                              OrderInfoModel(
                                header: "PARTS",
                                columnData: widget
                                    .repairOrderModel.repairDetailsModel!.parts!
                                    .map((part) {
                                  return part.title ?? "";
                                }).toList(),
                              ),
                              OrderInfoModel(
                                header: "QTY",
                                columnData: widget
                                    .repairOrderModel.repairDetailsModel!.parts!
                                    .map((part) {
                                  return part.quantity.toString();
                                }).toList(),
                              ),
                              OrderInfoModel(
                                header: "RATE",
                                columnData: widget
                                    .repairOrderModel.repairDetailsModel!.parts!
                                    .map((part) {
                                  return part.rate.toString();
                                }).toList(),
                              ),
                              if (widget.repairOrderModel.gstIncluded ??
                                  false) ...[
                                OrderInfoModel(
                                  header: "TAX RATE",
                                  columnData: widget.repairOrderModel
                                      .repairDetailsModel!.parts!
                                      .map((service) {
                                    return service.gstRate.toString();
                                  }).toList(),
                                ),
                                OrderInfoModel(
                                  header: "TAX AMOUNT",
                                  columnData: widget.repairOrderModel
                                      .repairDetailsModel!.parts!
                                      .map((service) {
                                    return service.partsGst.toString();
                                  }).toList(),
                                ),
                              ],
                              OrderInfoModel(
                                header: "AMOUNT",
                                columnData: widget
                                    .repairOrderModel.repairDetailsModel!.parts!
                                    .map((part) {
                                  return ((part.amount ?? 0) +
                                          (part.partsGst ?? 0))
                                      .toString();
                                }).toList(),
                              ),
                            ],
                            totalTitle: "Total",
                            totalAmount:
                                "${widget.repairOrderModel.repairDetailsModel?.partsTotal?.toStringAsFixed(2)}",
                          ),
                          HeightBox(10),
                          OrderInfoContainer(
                            cgst: (tax / 2).toString(),
                            sgst: (tax / 2).toString(),
                            igst: (tax).toString(),
                            gstIncluded: [
                              widget.repairOrderModel.gstIncluded ?? false,
                              widget.repairOrderModel.isigst ?? false
                            ],
                            totalTitle: "PAYABLE AMOUNT",
                            totalAmount: widget
                                .repairOrderModel.repairDetailsModel!.total
                                .toString(),
                            orderInfoModels: [
                              OrderInfoModel(header: 'SUMMARY', columnData: []),
                              OrderInfoModel(
                                header: '',
                                columnData: ["SUB TOTAL :"],
                              ),
                              OrderInfoModel(
                                header: '',
                                columnData: [
                                  "${Constants.rupeeSign} ${subTotal}",
                                ],
                              ),
                            ],
                          ),
                          if (widget.repairOrderModel.repairDetailsModel
                                  ?.customerRemarks?.isNotEmpty ??
                              false) ...[
                            HeightBox(50),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 2),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Customer Remarks :",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  ...widget.repairOrderModel.repairDetailsModel!
                                      .customerRemarks!
                                      .mapIndexed((index, remark) {
                                    return Row(
                                      children: [
                                        WidthBox(15),
                                        Text("${index + 1}. $remark"),
                                      ],
                                    );
                                  }),
                                ],
                              ),
                            ),
                          ],
                          HeightBox(20),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Column(
                                  children: [
                                    if (_currentSelection == 0) ...[
                                      if (widget
                                              .repairOrderModel
                                              .repairDetailsModel
                                              ?.customerSignatureLink !=
                                          null) ...[
                                        Image.network(
                                          widget
                                                  .repairOrderModel
                                                  .repairDetailsModel
                                                  ?.customerSignatureLink ??
                                              "",
                                          height: 100,
                                          width: 100,
                                        )
                                      ],
                                      const Text(
                                        "Customer Signature",
                                      ),
                                    ],
                                    if (_currentSelection == 1) ...[
                                      if (widget
                                              .repairOrderModel
                                              .repairDetailsModel
                                              ?.businessOwnerSignatureLink !=
                                          null) ...[
                                        Image.network(
                                          widget
                                                  .repairOrderModel
                                                  .repairDetailsModel
                                                  ?.businessOwnerSignatureLink ??
                                              "",
                                          height: 100,
                                          width: 100,
                                        )
                                      ],
                                      const Text(
                                        "Business Owner Signature",
                                      ),
                                    ]
                                  ],
                                )
                              ],
                            ),
                          ),
                          HeightBox(50),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Future<void> savePdf() async {
  //   RepairOrderDetailsController repairOrderDetailsController =
  //       Get.find<RepairOrderDetailsController>();

  //   try {
  //     await Utils.requestPermissions(permissionsList: [
  //       Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
  //     ]);

  //     Directory? directory;

  //     // Get the path to the local storage
  //     if (Platform.isAndroid) {
  //       // Redirects it to download folder in android
  //       directory = Directory("/storage/emulated/0/Download");
  //     } else {
  //       directory = await getApplicationDocumentsDirectory();
  //     }
  //     final path = _currentSelection == 0
  //         ? '${directory.path}/${widget.repairOrderModel?.customerDetailsModel?.username?.replaceAll(" ", "_")}_${widget.repairOrderModel?.orderId}_repair_order.pdf'
  //         : '${directory.path}/${widget.repairOrderModel?.customerDetailsModel?.username?.replaceAll(" ", "_")}_${widget.repairOrderModel?.jobCardId}_invoice.pdf';

  //     final pdf = await _capturePdf();

  //     // final file = File(path);
  //     // await fileriteAsBytes(await pdf.save());

  //     await FileSaver.instance.saveFile(
  //       name: _currentSelection == 0
  //           ? "${widget.repairOrderModel.customerDetailsModel?.username?.replaceAll(" ", "_")}_${widget..repairOrderModel.orderId}_repair_order"
  //           : "${widget.repairOrderModel.customerDetailsModel?.username?.replaceAll(" ", "_")}_${widget.repairOrderModel?.jobCardId}_invoice",
  //       ext: 'pdf',
  //       bytes: await pdf.save(),
  //       mimeType: MimeType.pdf,
  //     );

  //     Utils.showSnackBar(title: "File downloaded $path ");
  //   } catch (e) {
  //     Utils.showSnackBar(title: "Error $e");
  //   }
  // }

  Future<pw.Document> _capturePdf() async {
    RenderRepaintBoundary boundary = widget.globalKey.currentContext!
        .findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final buffer = byteData!.buffer.asUint8List();

    final pdf = pw.Document();
    final pdfImage = pw.MemoryImage(buffer);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Image(pdfImage);
        },
      ),
    );

    return pdf;
  }

  Future<void> _sharePdf(
    pw.Document pdf,
  ) async {
    // RepairOrderDetailsController repairOrderDetailsController =
    //     Get.find<RepairOrderDetailsController>();

    String pdfName = widget.repairOrderModel.customerDetailsModel?.username
            ?.replaceAll(' ', '_') ??
        "";

    if (_currentSelection == 0) {
      pdfName += "_${widget.repairOrderModel.orderId}_repair_order.pdf";
    } else {
      pdfName += "_${widget.repairOrderModel.jobCardId}_invoice.pdf";
    }
    await Printing.sharePdf(bytes: await pdf.save(), filename: pdfName);
  }
}
