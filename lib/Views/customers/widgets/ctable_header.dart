import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';

class CustomersTableHeader extends StatelessWidget {
  const CustomersTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      // height: 14,
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 228, 228, 228),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // const SizedBox(
          //     width: 60,
          //     child: Text('Sr No',
          //         textAlign: TextAlign.center,
          //         style: TextStyle(fontWeight: FontWeight.w600))),
          // const SizedBox(width: 20),
          // const SizedBox(width: 80, child: TableHeaderText(headerName: 'Name')),
          // const SizedBox(width: 71),
          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Workshop')),
          // const SizedBox(width: 10),
          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Contact No.')),
          // // const SizedBox(width: 10),
          // // const SizedBox(
          // //     width: 150, child: TableHeaderText(headerName: 'Email')),
          // const SizedBox(width: 10),
          // const SizedBox(
          //     width: 160, child: TableHeaderText(headerName: 'Last Visit')),
          // const SizedBox(width: 10),
          // const SizedBox(
          //     width: 160, child: TableHeaderText(headerName: 'Total Bill')),
          SizedBox(
              width: 70,
              child: Text('Sr No',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontWeight: FontWeight.w600))),
          SizedBox(width: 20),
          Expanded(child: TableHeaderText(headerName: 'Name')),
          SizedBox(width: 60),
          Expanded(child: TableHeaderText(headerName: 'Workshop')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'Contact No.')),
          // const SizedBox(width: 10),
          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Email')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'Last Visit')),
          SizedBox(width: 10),
          Expanded(child: TableHeaderText(headerName: 'Total Bill')),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //       width: 60,
          //       child: Transform.scale(
          //         scale: .65,
          //         child: CupertinoSwitch(
          //           value: true,
          //           onChanged: (value) {},
          //         ),
          //       )),
          // ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         CupertinoIcons.star,
          //         size: 22,
          //         color: Colors.amber,
          //       ),
          //     ),
          //   ),
          // ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         Icons.delete,
          //         color: themeColor,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

          // const SizedBox(width: 20),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Contact')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'Category')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150, child: TableHeaderText(headerName: 'City')),
          // const SizedBox(width: 5),

          // const SizedBox(
          //     width: 150,
          //     child: TableHeaderText(headerName: 'Sponsored Priority')),
          // const SizedBox(width: 5),

          // const TableHeaderText(headerName: 'City'),
          // const SizedBox(width: 5),

          // const TableHeaderText(headerName: 'Price'),
          // const SizedBox(width: 5),

          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         CupertinoIcons.pencil,
          //         size: 22,
          //       ),
          //     ),
          //   ),
          // ),