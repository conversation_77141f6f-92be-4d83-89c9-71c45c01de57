import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

Future<Map<String, dynamic>> nextFetch(DocumentSnapshot<Object?> lastDoc,
    int currentPage, bool fromAllCustomers, String? selectedworkshop) async {
  try {
    if (fromAllCustomers) {
      final data = await FBFireStore.customers
          .orderBy('lowername')
          .startAfterDocument(lastDoc)
          .limit(25)
          .get();
      return {
        'data': data.docs.map((e) => CustomersModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.length < 25 ? null : data.docs[data.docs.length - 1],
        'currentPage': currentPage + 1,
      };
    } else {
      final data = await FBFireStore.customers
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('lowername')
          .startAfterDocument(lastDoc)
          .limit(25)
          .get();
      return {
        'data': data.docs.map((e) => CustomersModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.length < 25 ? null : data.docs[data.docs.length - 1],
        'currentPage': currentPage + 1,
      };
    }
  } catch (e) {
    return {
      'data': [],
      'lastDoc': null,
      'currentPage': currentPage,
    };
  }
}

Future<Map<String, dynamic>> previousFetch(DocumentSnapshot<Object?> firstDoc,
    int currentPage, bool fromAllCustomers, String? selectedworkshop) async {
  try {
    if (fromAllCustomers) {
      final data = await FBFireStore.customers
          .orderBy('lowername')
          .endBeforeDocument(firstDoc)
          .limit(25)
          .get();
      return {
        'data': data.docs.map((e) => CustomersModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.isNotEmpty ? data.docs[data.docs.length - 1] : null,
        'currentPage': currentPage > 0 ? currentPage - 1 : 0,
      };
    } else {
      final data = await FBFireStore.customers
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('lowername')
          .endBeforeDocument(firstDoc)
          .limit(25)
          .get();
      return {
        'data': data.docs.map((e) => CustomersModel.fromSnap(e)).toList(),
        'lastDoc':
            data.docs.isNotEmpty ? data.docs[data.docs.length - 1] : null,
        'currentPage': currentPage > 0 ? currentPage - 1 : 0,
      };
    }
  } catch (e) {
    return {
      'data': [],
      'lastDoc': null,
      'currentPage': currentPage,
    };
  }
}
