import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/ctable_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/customer_tile.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> {
  String? selectedworkshop;
  fetchcusdata() async {
    cusfilteredList2 =
        await FBFireStore.customers.get().then((value) => value.docs.map((e) {
              return CustomersModel.fromSnap(e);
            }).toList());

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    fetchcusdata();
  }

  final searchController = TextEditingController();

  List<CustomersModel> cusfilteredList = [];

  List<CustomersModel> cusfilteredList2 = [];
  List<CustomersModel> cusfilteredList3 = [];

  getcusFilteredList(String value) async {
    try {
      cusfilteredList = (await FBFireStore.customers
              .where(Filter('username',
                  isGreaterThanOrEqualTo: value.toLowerCase()))
              .where(Filter('username', isLessThan: '${value.toLowerCase()}z'))
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();
      setState(() {});
      print('>>>>>>> ${cusfilteredList.length}');
    } catch (e) {}
  }

  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      // final fdata = ctrl.franchises.firstWhereOrNull(
      //     (element) => element.docID == ctrl.franchises.first.docID);
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: "Customers",
                onPressed: () {},
                button: false,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  SearchField(
                    searchController: searchController,
                    onChanged: (value) {
                      getcusFilteredList(searchController.text);
                    },
                  ),

                  const SizedBox(width: 10),
                  DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                    isExpanded: true,
                    focusColor: Colors.transparent,
                    dropdownColor: Colors.white,
                    decoration: InputDecoration(
                        hintText: "Select Workshop",
                        hintStyle: const TextStyle(fontSize: 30),
                        constraints:
                            const BoxConstraints(maxWidth: 300, maxHeight: 45),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5))),
                    value: selectedworkshop,
                    items: [
                      ...List.generate(
                        ctrl.franchises.length,
                        (index) {
                          return DropdownMenuItem(
                              value: ctrl.franchises[index].docID,
                              child: Text(
                                  ctrl.franchises[index].garageName ?? ""));
                        },
                      ),
                    ],
                    onChanged: (value) async {
                      if (value == null) {
                        return;
                      }
                      selectedworkshop = value;
                      getdplist(value);
                    },
                  )),
                  const SizedBox(width: 10),

                  // Text(
                  //   "Total Customers : ${cusfilteredList.length}",
                  //   style: const TextStyle(
                  //     fontWeight: FontWeight.w400,
                  //     fontSize: 18,
                  //   ),
                  // ),
                ],
              ),
              const SizedBox(height: 25),
              const CustomersTableHeader(),
              CustomersTile(
                customers:
                    searchController.text.isNotEmpty || selectedworkshop != null
                        ? searchController.text.isNotEmpty
                            ? cusfilteredList
                            : cusfilteredList3
                        : cusfilteredList2,
              ),
            ],
          ),
        ),
      );
    });
  }
}
