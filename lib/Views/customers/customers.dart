import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/ctable_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/customer_tile.dart';
import 'package:speed_force_admin/Views/customers/widgets/methods.dart';
import 'package:speed_force_admin/Views/franchises/widgets/pagination_ui.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> {
  String? selectedworkshop;
  fetchcusdata() async {
    try {
      final firstData =
          await FBFireStore.customers.limit(25).orderBy('lowername').get();
      customerList = firstData.docs.map((e) {
        return CustomersModel.fromSnap(e);
      }).toList();
      lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
      setState(() {});
    } catch (e) {
      debugPrint('Error fetching customers: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    fetchcusdata();
  }

  final searchController = TextEditingController();

  List<CustomersModel> customerList = [];
  List<CustomersModel> customerSearchList = [];
  DocumentSnapshot<Object?>? lastDoc;
  int currentPage = 0;
  getcusFilteredList(String value) async {
    try {
      final firstData = selectedworkshop == null
          ? (await FBFireStore.customers
              .where('lowername', isGreaterThanOrEqualTo: value)
              .where('lowername', isLessThanOrEqualTo: "$value\uf7ff")
              // .where(Filter('username',
              //     isGreaterThanOrEqualTo: value.toLowerCase()))
              // .where(Filter('username', isLessThan: '${value.toLowerCase()}z'))
              .limit(30)
              .get())
          : (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .where('lowername', isGreaterThanOrEqualTo: value)
              .where('lowername', isLessThanOrEqualTo: "$value\uf7ff")
              // .where(Filter('username',
              //     isGreaterThanOrEqualTo: value.toLowerCase()))
              // .where(Filter('username', isLessThan: '${value.toLowerCase()}z'))
              .limit(30)
              .get());

      customerSearchList =
          firstData.docs.map((e) => CustomersModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getdplist(String value) async {
    try {
      QuerySnapshot<Map<String, dynamic>> firstData;

      if (selectedworkshop != null) {
        firstData = await FBFireStore.customers
            .where('franchiseId', isEqualTo: selectedworkshop)
            .orderBy('lowername')
            .limit(25)
            .get();
      } else {
        // If no workshop selected, fetch all customers
        firstData =
            await FBFireStore.customers.orderBy('lowername').limit(25).get();
      }

      customerList =
          firstData.docs.map((e) => CustomersModel.fromSnap(e)).toList();
      lastDoc = firstData.docs.isNotEmpty ? firstData.docs.last : null;
      currentPage = 0; // Reset to first page

      setState(() {});
    } catch (e) {
      debugPrint('Error fetching workshop customers: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ctrl.franchises.sort((a, b) => (a.garageName?.toLowerCase().trim() ?? '')
          .compareTo(b.garageName?.toLowerCase().trim() ?? ''));
      customerList.sort((a, b) => (a.username?.toLowerCase().trim() ?? '')
          .compareTo(b.username?.toLowerCase().trim() ?? ''));
      customerSearchList.sort((a, b) => (a.username?.toLowerCase().trim() ?? '')
          .compareTo(b.username?.toLowerCase().trim() ?? ''));
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: "Customers",
                onPressed: () {},
                button: false,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  SearchField(
                    searchController: searchController,
                    onChanged: (value) {
                      currentPage = 0;
                      if (value.trim().isEmpty) {
                        // If search is cleared, reload the current workshop data
                        customerSearchList = [];
                        if (selectedworkshop != null) {
                          getdplist(selectedworkshop!);
                        } else {
                          fetchcusdata();
                        }
                      } else {
                        getcusFilteredList(value.trim().toLowerCase());
                      }
                    },
                  ),

                  const SizedBox(width: 10),
                  Row(
                    children: [
                      DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                        isExpanded: true,
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        decoration: InputDecoration(
                            hintText: "Select Workshop",
                            hintStyle: const TextStyle(fontSize: 30),
                            constraints: const BoxConstraints(
                                maxWidth: 300, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        value: selectedworkshop,
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text("All Workshops"),
                          ),
                          ...List.generate(
                            ctrl.franchises.length,
                            (index) {
                              return DropdownMenuItem(
                                  value: ctrl.franchises[index].docID,
                                  child: Text(
                                      ctrl.franchises[index].garageName ??
                                          "Unknown"));
                            },
                          ),
                        ],
                        onChanged: (value) async {
                          currentPage = 0;
                          customerList = [];
                          customerSearchList = [];
                          lastDoc = null;
                          selectedworkshop = value;
                          searchController
                              .clear(); // Clear search when changing workshop
                          getdplist(value ?? '');
                        },
                      )),
                      TextButton(
                          onPressed: () {
                            currentPage = 0;
                            customerList = [];
                            customerSearchList = [];
                            lastDoc = null;
                            selectedworkshop = null;
                            searchController.clear();
                            fetchcusdata(); // Use fetchcusdata instead of getdplist
                          },
                          child: const Text('Clear'))
                    ],
                  ),
                  const SizedBox(width: 10),

                  // Show customer count
                  Expanded(
                    child: Text(
                      "Showing: ${searchController.text.isNotEmpty ? customerSearchList.length : customerList.length} customers",
                      style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 25),
              const CustomersTableHeader(),
              CustomersTile(
                currentPage: currentPage,
                // key: ValueKey(DateTime.now()),
                customers:
                    searchController.text.isNotEmpty || selectedworkshop != null
                        ? searchController.text.isNotEmpty
                            ? customerSearchList
                            : customerList
                        : customerList,
              ),
              const SizedBox(height: 20),
              PaginationArrowsExpiry(
                previousBlocked: currentPage == 0,
                nextsBlocked: lastDoc == null || customerList.length < 25,
                onPrevious: () async {
                  if (currentPage > 0 && lastDoc != null) {
                    final data = await previousFetch(lastDoc!, currentPage,
                        selectedworkshop == null, selectedworkshop);
                    customerList = data['data'];
                    lastDoc = data['lastDoc'];
                    currentPage = data['currentPage'];
                    setState(() {});
                  }
                },
                onNext: () async {
                  if (lastDoc != null && customerList.length >= 25) {
                    final data = await nextFetch(lastDoc!, currentPage,
                        selectedworkshop == null, selectedworkshop);
                    if (data['data'].isNotEmpty) {
                      customerList = data['data'];
                      lastDoc = data['lastDoc'];
                      currentPage = data['currentPage'];
                      setState(() {});
                    }
                  }
                },
              )
            ],
          ),
        ),
      );
    });
  }
}
