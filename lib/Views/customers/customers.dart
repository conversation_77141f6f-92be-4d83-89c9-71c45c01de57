import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/ctable_header.dart';
import 'package:speed_force_admin/Views/customers/widgets/customer_tile.dart';
import 'package:speed_force_admin/Views/customers/widgets/methods.dart';
import 'package:speed_force_admin/Views/franchises/widgets/pagination_ui.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Customers extends StatefulWidget {
  const Customers({super.key});

  @override
  State<Customers> createState() => _CustomersState();
}

class _CustomersState extends State<Customers> {
  String? selectedworkshop;
  int? totalCustomers;
  fetchcusdata() async {
    totalCustomers = (await FBFireStore.customers.count().get()).count;
    final firstData =
        await FBFireStore.customers.limit(25).orderBy('lowername').get();
    customerList = firstData.docs.map((e) {
      return CustomersModel.fromSnap(e);
    }).toList();
    firstDoc = firstData.docs.first;
    lastDoc = firstData.docs.last;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    fetchcusdata();
  }

  final searchController = TextEditingController();

  List<CustomersModel> customerList = [];
  List<CustomersModel> customerSearchList = [];
  DocumentSnapshot<Object?>? lastDoc;
  DocumentSnapshot<Object?>? firstDoc;
  int currentPage = 0;
  getcusFilteredList(String value) async {
    try {
      final firstData = selectedworkshop == null
          ? (await FBFireStore.customers
              .where('lowername', isGreaterThanOrEqualTo: value)
              .where('lowername', isLessThanOrEqualTo: "$value\uf7ff")
              // .where(Filter('username',
              //     isGreaterThanOrEqualTo: value.toLowerCase()))
              // .where(Filter('username', isLessThan: '${value.toLowerCase()}z'))
              .limit(30)
              .get())
          : (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .where('lowername', isGreaterThanOrEqualTo: value)
              .where('lowername', isLessThanOrEqualTo: "$value\uf7ff")
              // .where(Filter('username',
              //     isGreaterThanOrEqualTo: value.toLowerCase()))
              // .where(Filter('username', isLessThan: '${value.toLowerCase()}z'))
              .limit(30)
              .get());

      customerSearchList =
          firstData.docs.map((e) => CustomersModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getdplist(String value) async {
    try {
      final firstData = await FBFireStore.customers
          .where('franchiseId', isEqualTo: selectedworkshop)
          .orderBy('lowername')
          .limit(25)
          .get();
      lastDoc = firstData.docs.length < 25 ? null : firstData.docs.last;
      firstDoc = firstData.docs.isEmpty ? null : firstData.docs.first;
      // currentPage = 1;
      customerList =
          firstData.docs.map((e) => CustomersModel.fromSnap(e)).toList();

      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ctrl.franchises.sort((a, b) => (a.garageName?.toLowerCase().trim() ?? '')
          .compareTo(b.garageName?.toLowerCase().trim() ?? ''));
      customerList.sort((a, b) => (a.username?.toLowerCase().trim() ?? '')
          .compareTo(b.username?.toLowerCase().trim() ?? ''));
      customerSearchList.sort((a, b) => (a.username?.toLowerCase().trim() ?? '')
          .compareTo(b.username?.toLowerCase().trim() ?? ''));

      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: "Customers (${totalCustomers ?? ''})",
                onPressed: () {},
                button: false,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  SearchField(
                    searchController: searchController,
                    onChanged: (value) {
                      currentPage = 0;
                      getcusFilteredList(
                          searchController.text.trim().toLowerCase());
                    },
                  ),

                  const SizedBox(width: 10),
                  Row(
                    children: [
                      DropdownButtonHideUnderline(
                          child: DropdownButtonFormField(
                        isExpanded: true,
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        decoration: InputDecoration(
                            hintText: "Select Workshop",
                            hintStyle: const TextStyle(fontSize: 30),
                            constraints: const BoxConstraints(
                                maxWidth: 300, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        value: selectedworkshop,
                        items: [
                          ...List.generate(
                            ctrl.franchises.length,
                            (index) {
                              return DropdownMenuItem(
                                  value: ctrl.franchises[index].docID,
                                  child: Text(
                                      ctrl.franchises[index].garageName ?? ""));
                            },
                          ),
                        ],
                        onChanged: (value) async {
                          if (value == null) {
                            return;
                          }
                          currentPage = 0;
                          customerList = [];
                          customerSearchList = [];
                          lastDoc = null;
                          firstDoc = null;
                          selectedworkshop = value;
                          getdplist(value);
                        },
                      )),
                      TextButton(
                          onPressed: false
                              ? () async {
                                  final data = await FBFireStore.customers
                                      .where('franchiseId',
                                          isEqualTo: selectedworkshop)
                                      .get();
                                  final customers = data.docs
                                      .map((e) => CustomersModel.fromSnap(e))
                                      .toList();
                                  int i = 0;
                                  for (var element in customers) {
                                    i++;
                                    print(i);
                                    await FBFireStore.customers
                                        .doc(element.docId)
                                        .update({
                                      'lowername': element.username
                                              ?.trim()
                                              .toLowerCase() ??
                                          ""
                                    });
                                  }
                                }
                              : () {
                                  currentPage = 0;
                                  customerList = [];
                                  customerSearchList = [];
                                  lastDoc = null;
                                  firstDoc = null;
                                  selectedworkshop = null;
                                  fetchcusdata();
                                },
                          child: const Text('Clear'))
                    ],
                  ),
                  const SizedBox(width: 10),

                  // Text(
                  //   "Total Customers : ${cusfilteredList.length}",
                  //   style: const TextStyle(
                  //     fontWeight: FontWeight.w400,
                  //     fontSize: 18,
                  //   ),
                  // ),
                ],
              ),
              const SizedBox(height: 25),
              const CustomersTableHeader(),
              CustomersTile(
                currentPage: currentPage,
                // key: ValueKey(DateTime.now()),
                customers:
                    searchController.text.isNotEmpty || selectedworkshop != null
                        ? searchController.text.isNotEmpty
                            ? customerSearchList
                            : customerList
                        : customerList,
              ),
              const SizedBox(height: 20),
              PaginationArrowsExpiry(
                previousBlocked: currentPage == 0,
                nextsBlocked: lastDoc == null,
                onPrevious: () async {
                  //prinyt alll the data passed down

                  final data = await previousFetch(firstDoc!, currentPage,
                      selectedworkshop == null, selectedworkshop);
                  customerList = data['data'];
                  lastDoc = data['lastDoc'];
                  currentPage = data['currentPage'];
                  firstDoc = data['firstDoc'];
                  setState(() {});
                },
                onNext: () async {
                  final data = await nextFetch(lastDoc!, currentPage,
                      selectedworkshop == null, selectedworkshop);
                  customerList = data['data'];
                  lastDoc = data['lastDoc'];
                  currentPage = data['currentPage'];
                  firstDoc = data['firstDoc'];

                  setState(() {});
                },
              )
            ],
          ),
        ),
      );
    });
  }
}
