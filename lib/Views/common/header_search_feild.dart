import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../shared/theme.dart';

class SearchField extends StatelessWidget {
  const SearchField({
    super.key,
    required this.searchController,
    this.onChanged,
  });
  final TextEditingController searchController;
  final Function(String)? onChanged;
  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 450),
      child: SizedB<PERSON>(
        height: 45,
        child: TextFormField(
          controller: searchController,
          cursorHeight: 20,
          onChanged: onChanged,
          // Get.find<HomeCtrl>().update(),
          // onChanged: onChanged,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(),
            fillColor: Colors.grey.shade100,
            filled: true,
            prefixIcon: const Icon(
              CupertinoIcons.search,
              size: 22,
              color: themeColor,
            ),
            border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(7)),
            hintText: 'Search',
            hintStyle: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }
}
