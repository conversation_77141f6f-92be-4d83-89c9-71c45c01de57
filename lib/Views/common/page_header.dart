import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/theme.dart';

class PageHeaderWithButton extends StatelessWidget {
  const PageHeaderWithButton({
    super.key,
    required this.title,
    this.icon,
    required this.onPressed,
    this.buttonName,
    this.button = false,
    this.actions,
  });
  final String title;
  final IconData? icon;
  final String? buttonName;
  final Function onPressed;
  final bool button;
  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                      fontSize: 25,
                      fontWeight: FontWeight.bold,
                      color: Colors.red),
                ),
                const SizedBox(width: 20),
                if (actions != null) ...[...actions ?? []]
              ],
            ),
            if (button)
              ElevatedButton.icon(
                style: ButtonStyle(
                    backgroundColor: const WidgetStatePropertyAll(Colors.red),
                    padding: const WidgetStatePropertyAll(
                        EdgeInsets.symmetric(horizontal: 20, vertical: 20)),
                    side: const WidgetStatePropertyAll(
                        BorderSide(color: Colors.transparent)),
                    shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5),
                    ))),
                // style: ElevatedButton.styleFrom(
                //   backgroundColor: themeColor,
                //   elevation: 0,
                //   foregroundColor: Colors.white,
                //   shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(4)),
                //   // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                // ),
                onPressed: () => onPressed(),
                // icon: Icon(
                //   icon,
                //   size: 20,
                // ),
                label: Text(buttonName ?? "",
                    style: const TextStyle(fontSize: 16, color: Colors.white)),
              ),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}

class PageHeaderWithTrailingAndBack extends StatelessWidget {
  const PageHeaderWithTrailingAndBack({
    super.key,
    required this.title,
    this.trailing,
    required this.showTrailing,
  });
  final String title;
  final Widget? trailing;
  final bool showTrailing;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: const Icon(
                      CupertinoIcons.arrow_left,
                      color: themeColor,
                    )),
                const SizedBox(width: 10),
                Text(title,
                    style: const TextStyle(fontSize: 28, color: themeColor)),
              ],
            ),
            if (showTrailing) trailing ?? const SizedBox(),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}

class PageHeaderWithTrailing extends StatelessWidget {
  const PageHeaderWithTrailing({
    super.key,
    required this.title,
    this.trailing,
    required this.showTrailing,
  });
  final String title;
  final Widget? trailing;
  final bool showTrailing;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style: const TextStyle(fontSize: 28, color: themeColor)),
            if (showTrailing) trailing ?? const SizedBox(),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}
