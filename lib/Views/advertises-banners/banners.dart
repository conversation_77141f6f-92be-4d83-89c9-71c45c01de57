import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/advertises-banners/widgets/add_bannerform.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Banners extends StatelessWidget {
  const Banners({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      print("uid : ${FBAuth.auth.currentUser?.uid.toString()}");
      return Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            PageHeaderWithButton(
              title: 'Banners',
              onPressed: () => addbannerform(context, null, true),
              button: true,
              buttonName: 'ADD BANNER',
              actions: const [
                Text(
                  "[Note: Image ratio must be 1/4]",
                  style: TextStyle(fontSize: 16),
                )
              ],
            ),
            const SizedBox(height: 30),
            AlignedGridView.count(
              shrinkWrap: true,
              itemCount: ctrl.advertise.length,
              itemBuilder: (BuildContext context, int index) {
                return SizedBox(
                  height: 200,
                  child: InkWell(
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    onTap: () =>
                        addbannerform(context, ctrl.advertise[index], false),
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Card(
                        elevation: 1,
                        color: Colors.white70,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                const Spacer(),
                                IconButton(
                                  hoverColor: Colors.transparent,
                                  onPressed: () =>
                                      _showDeleteDialog(context, ctrl, index),
                                  icon: const Icon(CupertinoIcons.multiply),
                                ),
                              ],
                            ),
                            const Spacer(),
                            Text(
                              "BANNER ${(index + 1).toString()}",
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 15,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
              crossAxisCount: 5,
            )
          ],
        ),
      );
    });
  }

  void _showDeleteDialog(BuildContext context, HomeCtrl ctrl, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Alert"),
        content: const Text("Are you sure you want to delete"),
        actions: [
          TextButton(
            onPressed: () async {
              await FBFireStore.advertises
                  .doc(ctrl.advertise[index].docId)
                  .delete();
              Navigator.of(context).pop();
            },
            child: const Text('Yes'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('No'),
          ),
        ],
      ),
    );
  }
}
