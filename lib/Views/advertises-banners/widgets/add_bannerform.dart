import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/models/advertise_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/image_picker.dart';

Future<dynamic> addbannerform(
    BuildContext context, AdvertiseModel? advertisedata,
    [bool edit = false]) {
  TextEditingController redirectLinkcontroller = TextEditingController();

  if (advertisedata != null) {
    redirectLinkcontroller.text = advertisedata.redirectLink.toString();
  }

  bool onSubmitLoad = false;

  SelectedImage? imageFileList;

  List<String> imageurl = [];

  Future<String?> uploadBannerImage(SelectedImage imageFile) async {
    try {
      final path =
          "Images/${DateTime.now().millisecondsSinceEpoch}.${imageFile.extention}";
      print('1');
      final imageRef = FBStorage.fbstore.ref().child(path);
      print('2');
      final task = await imageRef.putData(imageFile.uInt8List);
      print('3');
      var downloadurl = await task.ref.getDownloadURL();
      print('4');
      imageurl.add(downloadurl);
      print('5');

      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      print('6');
      debugPrint(e.toString());
      print('7');
      return null;
    }
  }

  return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text("Add Banner"),
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: const Icon(Icons.close),
                  )
                ]),
            content: SizedBox(
                width: 800,
                height: 250,
                child: Column(
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ElevatedButton(
                            onPressed: () async {
                              imageFileList = await ImagePickerService()
                                  .pickImageNew(context, useCompressor: true);
                              setState2(() {});
                            },
                            child: const Icon(
                                CupertinoIcons.photo_fill_on_rectangle_fill)),
                        const SizedBox(width: 10),
                        SizedBox(
                          width: 700,
                          child: TextFormField(
                            controller: redirectLinkcontroller,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10))),
                                labelText: 'Redirect Link'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 25),
                    SizedBox(
                      height: 100,
                      width: 80,
                      child: Stack(
                        children: [
                          // Display network image
                          if (advertisedata?.imageUrl != null)
                            Image.network(
                              advertisedata!.imageUrl!,
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return const Center(
                                    child: CircularProgressIndicator());
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return const Center(child: Icon(Icons.error));
                              },
                            ),
                          // Display memory image
                          if (imageFileList != null)
                            Image.memory(
                              imageFileList!.uInt8List,
                              fit: BoxFit.cover,
                            ),
                          // Cancel button
                          Align(
                            alignment: Alignment.topRight,
                            child: IconButton(
                              icon: const Icon(Icons.cancel),
                              onPressed: () {
                                if (advertisedata != null) {
                                  FBFireStore.advertises
                                      .doc(advertisedata.docId)
                                      .update(
                                          {'imageUrl': FieldValue.delete()});
                                  setState2(() {
                                    advertisedata.imageUrl = null;
                                  });
                                } else {
                                  imageFileList = null;
                                  setState2(() {});
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    onSubmitLoad
                        ? const Center(
                            child: CircularProgressIndicator(
                                color: Colors.red, strokeWidth: 3.5))
                        : ElevatedButton(
                            style: ButtonStyle(
                                backgroundColor:
                                    const WidgetStatePropertyAll(Colors.red),
                                padding: const WidgetStatePropertyAll(
                                    EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 20)),
                                side: const WidgetStatePropertyAll(
                                    BorderSide(color: Colors.transparent)),
                                shape: WidgetStatePropertyAll(
                                    RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ))),
                            onPressed: () async {
                              onSubmitLoad = true;
                              try {
                                setState2(() {});
                                final finalimageurl =
                                    await uploadBannerImage(imageFileList!);

                                // print(".final image url....$finalimageurl");
                                // print(
                                //     ". redirectLinkcontroller.text....${redirectLinkcontroller.text}");

                                // print("edit : $edit");

                                edit
                                    ? FBFireStore.advertises.add({
                                        'imageUrl': finalimageurl,
                                        'redirectLink':
                                            redirectLinkcontroller.text,
                                      })
                                    : FBFireStore.advertises
                                        .doc(advertisedata?.docId)
                                        .update({
                                        'imageUrl': finalimageurl,
                                        'redirectLink':
                                            redirectLinkcontroller.text,
                                      });
                              } catch (e) {
                                debugPrint(e.toString());
                              }
                              context.pop();
                            },
                            child: const Text("Submit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16)))
                  ],
                )),
            actionsAlignment: MainAxisAlignment.start,
          );
        });
      });
}
