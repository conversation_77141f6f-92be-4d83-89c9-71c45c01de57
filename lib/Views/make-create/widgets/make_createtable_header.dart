import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';
import '../../../shared/theme.dart';

class MakeCreateTableHeader extends StatelessWidget {
  const MakeCreateTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 228, 228, 228),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(
              width: 60,
              child: Text('Sr No',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontWeight: FontWeight.w600))),
          const SizedBox(width: 20),
          const SizedBox(
              width: 80, child: TableHeaderText(headerName: 'Model')),
          const SizedBox(width: 10),
          const SizedBox(
              width: 150, child: TableHeaderText(headerName: 'Brand Name')),
          const SizedBox(width: 10),
          Opacity(
            opacity: 0,
            child: SizedBox(
                width: 60,
                child: Transform.scale(
                  scale: .65,
                  child: CupertinoSwitch(
                    value: true,
                    onChanged: (value) {},
                  ),
                )),
          ),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  CupertinoIcons.star,
                  size: 22,
                  color: Colors.amber,
                ),
              ),
            ),
          ),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.delete,
                  color: themeColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
