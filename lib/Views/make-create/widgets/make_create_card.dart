import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/make-create/makecreatemodel.dart';
import 'package:speed_force_admin/Views/make-create/widgets/makecreate_form.dart';
import 'package:speed_force_admin/models/makemodelsmodel.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class MakeCreateCard extends StatefulWidget {
  const MakeCreateCard({super.key, this.searchedText});
  final String? searchedText;
  @override
  State<MakeCreateCard> createState() => _MakeCreateCardState();
}

TextEditingController companycontroller = TextEditingController();
TextEditingController modelscontroller = TextEditingController();
TextEditingController makemodelIdcontroller = TextEditingController();
TextEditingController franchiseIdcontroller = TextEditingController();
bool loading = false;

class _MakeCreateCardState extends State<MakeCreateCard> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      print(searchController.text);
      ctrl.makecreatemodel.sort((a, b) =>
          (a.company?.toLowerCase().trim() ?? '')
              .compareTo(b.company?.toLowerCase().trim() ?? ''));
      final filteredList = widget.searchedText == null
          ? ctrl.makecreatemodel
          : ctrl.makecreatemodel
              .where((element) => element.company!
                  .toLowerCase()
                  .contains(widget.searchedText!.toLowerCase()))
              .toList();
      return AlignedGridView.count(
        shrinkWrap: true,
        itemCount: filteredList.length,
        itemBuilder: (BuildContext context, int index) {
          return SizedBox(
            height: 200,
            // width: 10,
            child: InkWell(
              overlayColor: const WidgetStatePropertyAll(Colors.transparent),
              onTap: () =>
                  addmakecreateform(context, false, filteredList[index]),
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Card(
                  elevation: 1,
                  color: Colors.white70,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          const Spacer(),
                          IconButton(
                            hoverColor: Colors.transparent,
                            onPressed: () => deleteMakeCreateContainer(
                                filteredList[index], index, context),
                            icon: const Icon(CupertinoIcons.multiply),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Text(
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                          ),
                          filteredList[index].company?.toUpperCase() ?? ""),
                      const SizedBox(
                        height: 15,
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        crossAxisCount: 5,
      );
    });
  }

  Future<dynamic> deleteMakeCreateContainer(
      MakeModelsModel data, int index, BuildContext context) {
    return showDialog(
      builder: (context) => AlertDialog(
        title: const Text("Alert"),
        content: const Text("Are you sure you want to delete"),
        actions: loading
            ? [
                const Center(
                  child: SizedBox(
                    height: 25,
                    width: 25,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                    ),
                  ),
                )
              ]
            : [
                TextButton(
                    onPressed: () async {
                      FBFireStore.vehiclecompanies
                          .doc(data.makeModelId)
                          .delete();
                      Navigator.of(context).pop();
                    },
                    child: const Text('Yes')),
                TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('No')),
              ],
      ),
      context: context,
    );
  }
}
