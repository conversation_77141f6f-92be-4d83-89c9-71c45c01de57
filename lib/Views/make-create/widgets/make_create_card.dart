import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/make-create/widgets/makecreate_form.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class MakeCreateCard extends StatefulWidget {
  const MakeCreateCard({super.key});

  @override
  State<MakeCreateCard> createState() => _MakeCreateCardState();
}

TextEditingController companycontroller = TextEditingController();
TextEditingController modelscontroller = TextEditingController();
TextEditingController makemodelIdcontroller = TextEditingController();
TextEditingController franchiseIdcontroller = TextEditingController();
bool loading = false;

class _MakeCreateCardState extends State<MakeCreateCard> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return AlignedGridView.count(
        shrinkWrap: true,
        itemCount: ctrl.makecreatemodel.length,
        itemBuilder: (BuildContext context, int index) {
          return SizedBox(
            height: 200,
            // width: 10,
            child: InkWell(
              overlayColor: const WidgetStatePropertyAll(Colors.transparent),
              onTap: () => addmakecreateform(
                  context, false, ctrl.makecreatemodel[index]),
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Card(
                  elevation: 1,
                  color: Colors.white70,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          const Spacer(),
                          IconButton(
                            hoverColor: Colors.transparent,
                            onPressed: () =>
                                deleteMakeCreateContainer(ctrl, index, context),
                            icon: const Icon(CupertinoIcons.multiply),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Text(
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                          ),
                          ctrl.makecreatemodel[index].company?.toUpperCase() ??
                              ""),
                      const SizedBox(
                        height: 15,
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
        crossAxisCount: 5,
      );
    });
  }

  Future<dynamic> deleteMakeCreateContainer(
      HomeCtrl ctrl, int index, BuildContext context) {
    return showDialog(
      builder: (context) => AlertDialog(
        title: const Text("Alert"),
        content: const Text("Are you sure you want to delete"),
        actions: loading
            ? [
                const Center(
                  child: SizedBox(
                    height: 25,
                    width: 25,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                    ),
                  ),
                )
              ]
            : [
                TextButton(
                    onPressed: () async {
                      FBFireStore.vehiclecompanies
                          .doc(ctrl.makecreatemodel[index].makeModelId)
                          .delete();
                      Navigator.of(context).pop();
                    },
                    child: const Text('Yes')),
                TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('No')),
              ],
      ),
      context: context,
    );
  }
}
