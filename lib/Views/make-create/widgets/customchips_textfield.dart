import 'package:flutter/material.dart';

class CustomChipsTextfield extends StatefulWidget {
  const CustomChipsTextfield({super.key, required this.customChipsController});

  final CustomChipsController customChipsController;

  @override
  State<CustomChipsTextfield> createState() => _CustomChipsTextfieldState();
}

class _CustomChipsTextfieldState extends State<CustomChipsTextfield> {
  TextEditingController controller = TextEditingController();

  FocusNode focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxHeight: MediaQuery.sizeOf(context).height * .4,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            focusNode: focusNode,
            controller: controller,
            onSubmitted: (value) {
              widget.customChipsController.chipsList.add(value);
              controller.clear();
              focusNode.requestFocus();
              setState(() {});
            },
            cursorHeight: 10,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.all(10),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.black),
                borderRadius: BorderRadius.all(
                  Radius.circular(10),
                ),
              ),
              labelText: 'Models',
              hintText: ' Hit enter once written',
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: Wrap(
                  children: [
                    ...widget.customChipsController.chipsList.map((chip) {
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 5),
                        decoration: (BoxDecoration(
                            color: Colors.grey,
                            borderRadius: BorderRadius.circular(10))),
                        child: Row(
                          textDirection: TextDirection.ltr,
                          mainAxisSize: MainAxisSize.min,
                          verticalDirection: VerticalDirection.down,
                          children: [
                            const SizedBox(
                              width: 5,
                            ),
                            Text(
                              chip,
                              style: const TextStyle(color: Colors.white),
                            ),
                            IconButton(
                              onPressed: () {
                                final int index = widget
                                    .customChipsController.chipsList
                                    .indexOf(chip);

                                widget.customChipsController.chipsList
                                    .removeAt(index);
                                setState(() {});
                              },
                              icon: const Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      );
                    })
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class CustomChipsController {
  List<String> chipsList = [];
}
