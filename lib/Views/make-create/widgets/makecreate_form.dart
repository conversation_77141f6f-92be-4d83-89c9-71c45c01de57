import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Views/make-create/widgets/customchips_textfield.dart';
import '../../../models/makemodelsmodel.dart';
import '../../../shared/firebase.dart';

Future<dynamic> addmakecreateform(
    BuildContext context, bool addNew, MakeModelsModel? makemodeldata) {
  TextEditingController makemodelIdcontroller = TextEditingController();
  // TextEditingController franchiseIdcontroller = TextEditingController();
  TextEditingController companycontroller = TextEditingController();
  bool onSubmitLoad = false;

  CustomChipsController customChipsController = CustomChipsController();

  assignData() {
    if (!addNew) {
      customChipsController.chipsList = makemodeldata?.models ?? [];
      makemodelIdcontroller.text = makemodeldata?.makeModelId ?? "";
      // franchiseIdcontroller.text = makemodeldata?.franchiseId ?? "";
      companycontroller.text = makemodeldata?.company ?? "";
    }
  }

  return showDialog(
      context: context,
      builder: (context) {
        assignData();
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text("Make/Create Model"),
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: const Icon(Icons.close),
                  )
                ]),
            content: SizedBox(
                // width: 800,
                // height: 350,
                child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: companycontroller,
                  decoration: const InputDecoration(
                      border: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                          borderRadius: BorderRadius.all(Radius.circular(10))),
                      labelText: 'Company'),
                ),
                // const SizedBox(height: 20),
                // TextFormField(
                //   controller: franchiseIdcontroller,
                //   decoration: const InputDecoration(
                //       border: OutlineInputBorder(
                //           borderSide: BorderSide(color: Colors.black),
                //           borderRadius: BorderRadius.all(Radius.circular(10))),
                //       labelText: 'Franchise ID'),
                // ),
                const SizedBox(height: 20),
                // TextFormField(
                //   controller: makemodelIdcontroller,
                //   decoration: const InputDecoration(
                //       border: OutlineInputBorder(
                //           borderSide: BorderSide(color: Colors.black),
                //           borderRadius:
                //               BorderRadius.all(Radius.circular(10))),
                //       labelText: 'MakeModel ID'),
                // ),
                CustomChipsTextfield(
                  customChipsController: customChipsController,
                ),
                const SizedBox(height: 15),
                onSubmitLoad
                    ? const Center(
                        child: CircularProgressIndicator(
                            color: Colors.red, strokeWidth: 3.5))
                    : ElevatedButton(
                        style: ButtonStyle(
                            backgroundColor:
                                const WidgetStatePropertyAll(Colors.red),
                            padding: const WidgetStatePropertyAll(
                                EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 20)),
                            side: const WidgetStatePropertyAll(
                                BorderSide(color: Colors.transparent)),
                            shape:
                                WidgetStatePropertyAll(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ))),
                        onPressed: () async {
                          if (companycontroller.text.isEmpty) {
                            return;
                          }
                          onSubmitLoad = true;
                          setState2(() {});
                          if (addNew) {
                            final newMakeModelId =
                                await FBFireStore.vehiclecompanies.add({
                              'company': companycontroller.text,
                              'franchiseId': null,
                              'models': customChipsController.chipsList,
                            });

                            await FBFireStore.vehiclecompanies
                                .doc(newMakeModelId.id)
                                .update({
                              "makeModelId": newMakeModelId.id,
                            });
                          }
                          if (!addNew) {
                            await FBFireStore.vehiclecompanies
                                .doc(makemodeldata?.makeModelId ?? "")
                                .update({
                              'company': companycontroller.text,
                              'franchiseId': null,
                              'models': customChipsController.chipsList,
                            });
                          }

                          if (context.mounted) {
                            onSubmitLoad = false;
                            Navigator.pop(context);
                            setState2(() {});
                          }
                        },
                        child: const Text("Submit",
                            style:
                                TextStyle(color: Colors.white, fontSize: 16)))
              ],
            )),
            actionsAlignment: MainAxisAlignment.start,
          );
        });
      });
}
