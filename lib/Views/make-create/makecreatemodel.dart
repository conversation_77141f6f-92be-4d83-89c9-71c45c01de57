import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/header_search_feild.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/make-create/widgets/makecreate_form.dart';
import 'package:speed_force_admin/Views/make-create/widgets/make_create_card.dart';

class Makecreatemodel extends StatefulWidget {
  const Makecreatemodel({super.key});

  @override
  State<Makecreatemodel> createState() => _MakecreatemodelState();
}

TextEditingController searchController = TextEditingController();

class _MakecreatemodelState extends State<Makecreatemodel> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            button: true,
            buttonName: 'make/create',
            title: "Make/Create Model",
            onPressed: () {
              addmakecreateform(context, true, null);
            },
          ),
          const Si<PERSON><PERSON><PERSON>(height: 30),
          Search<PERSON>ield(searchController: searchController),
          const SizedBox(height: 30),
          const Expanded(child: MakeCreateCard()),
        ],
      ),
    );
  }
}
