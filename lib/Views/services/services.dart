import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/models/service_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';

class Services extends StatefulWidget {
  const Services({super.key});

  @override
  State<Services> createState() => _ServicesState();
}

class _ServicesState extends State<Services> {
  // Controllers
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _serviceNameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _gstRateController = TextEditingController();

  // State variables
  List<ServiceModel> allServices = [];
  List<ServiceModel> filteredServices = [];
  bool isLoading = false;
  bool isAddingService = false;
  String? serviceNameError;
  String? priceError;
  String? gstRateError;
  Timer? _debounceTimer;

  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _fetchServices();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _serviceNameController.dispose();
    _priceController.dispose();
    _gstRateController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _filterServices(_searchController.text);
    });
  }

  void _filterServices(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredServices = List.from(allServices);
      } else {
        filteredServices = allServices.where((service) {
          return service.name!.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _fetchServices() async {
    if (!mounted) return;
    setState(() {
      isLoading = true;
    });

    try {
      final QuerySnapshot snapshot =
          await FBFireStore.services.orderBy('name').get();

      if (mounted) {
        setState(() {
          allServices = snapshot.docs
              .map((doc) =>
                  ServiceModel.fromMap(doc.data() as Map<String, dynamic>))
              .toList();
          filteredServices = List.from(allServices);
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Error fetching services: $e');
      }
    }
  }

  Future<bool> _checkServiceNameExists(String serviceName) async {
    try {
      final QuerySnapshot snapshot = await FBFireStore.services.get();

      // Check for case-insensitive duplicate names
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final existingName = data['name']?.toString().toLowerCase() ?? '';
        if (existingName == serviceName.toLowerCase()) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  void _validateServiceName(String value) {
    setState(() {
      if (value.isEmpty) {
        serviceNameError = 'Service name is required';
      } else if (value.length < 2) {
        serviceNameError = 'Service name must be at least 2 characters';
      } else {
        serviceNameError = null;
      }
    });
  }

  void _validatePrice(String value) {
    setState(() {
      if (value.isEmpty) {
        priceError = 'Price is required';
      } else {
        final double? price = double.tryParse(value);
        if (price == null || price <= 0) {
          priceError = 'Please enter a valid price';
        } else {
          priceError = null;
        }
      }
    });
  }

  void _validateGstRate(String value) {
    setState(() {
      if (value.isEmpty) {
        gstRateError = 'GST rate is required';
      } else {
        final double? gstRate = double.tryParse(value);
        if (gstRate == null || gstRate < 0 || gstRate > 100) {
          gstRateError = 'Please enter a valid GST rate (0-100)';
        } else {
          gstRateError = null;
        }
      }
    });
  }

  Future<void> _addService() async {
    if (!_formKey.currentState!.validate()) return;

    final serviceName = _serviceNameController.text.trim();
    final price = double.tryParse(_priceController.text.trim());
    final gstRate = double.tryParse(_gstRateController.text.trim());

    if (serviceName.isEmpty || price == null || gstRate == null) {
      _showErrorSnackBar('Please fill all required fields correctly');
      return;
    }

    setState(() {
      isAddingService = true;
    });

    try {
      // Check if service name already exists (case-insensitive)
      final bool exists = await _checkServiceNameExists(serviceName);
      if (exists) {
        setState(() {
          serviceNameError = 'Service with this name already exists';
          isAddingService = false;
        });
        return;
      }

      final String serviceId = FBFireStore.services.doc().id;
      final ServiceModel newService = ServiceModel(
        serviceId: serviceId,
        name: serviceName.toUpperCase(), // Store in uppercase
        price: price,
        gstRate: gstRate,
      );

      await FBFireStore.services.doc(serviceId).set(newService.toMap());

      if (mounted) {
        setState(() {
          allServices.add(newService);
          _filterServices(_searchController.text);
          isAddingService = false;
        });

        _clearForm();
        _showSuccessSnackBar('Service added successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingService = false;
        });
        _showErrorSnackBar('Error adding service: $e');
      }
    }
  }

  void _clearForm() {
    _serviceNameController.clear();
    _priceController.clear();
    _gstRateController.clear();
    setState(() {
      serviceNameError = null;
      priceError = null;
      gstRateError = null;
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                PageHeaderWithButton(
                  title: "Services",
                  onPressed: () {},
                  button: false,
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddServiceDialog(),
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: const Text(
                    'Add Service',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 222, 61, 61),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Search Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search services by name...',
                  border: InputBorder.none,
                  icon: Icon(Icons.search, color: Colors.grey),
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Services List
            Expanded(
              child: isLoading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : filteredServices.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                allServices.isEmpty
                                    ? 'No services found. Add your first service!'
                                    : 'No services match your search.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildServicesTable(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    'Sr No',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Service Name',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Price (MRP)',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'GST Rate (%)',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Actions',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Table Body
          Expanded(
            child: ListView.builder(
              itemCount: filteredServices.length,
              itemBuilder: (context, index) {
                final service = filteredServices[index];
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[200]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          service.name ?? '',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '₹${service.price?.toStringAsFixed(2) ?? '0.00'}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          '${service.gstRate?.toStringAsFixed(1) ?? '0.0'}%',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              onPressed: () => _editService(service),
                              icon: const Icon(
                                Icons.edit,
                                color: Colors.blue,
                                size: 20,
                              ),
                              tooltip: 'Edit Service',
                            ),
                            IconButton(
                              onPressed: () => _deleteService(service),
                              icon: const Icon(
                                Icons.delete,
                                color: Colors.red,
                                size: 20,
                              ),
                              tooltip: 'Delete Service',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showAddServiceDialog() {
    _clearForm();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text(
                'Add New Service',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SizedBox(
                width: 400,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Name Field
                      const Text(
                        'Service Name *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _serviceNameController,
                        decoration: InputDecoration(
                          hintText: 'Enter service name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: serviceNameError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateServiceName(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Service name is required';
                          }
                          if (value.trim().length < 2) {
                            return 'Service name must be at least 2 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Price Field
                      const Text(
                        'Price (MRP) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter price',
                          prefixText: '₹ ',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: priceError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validatePrice(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Price is required';
                          }
                          final double? price = double.tryParse(value.trim());
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // GST Rate Field
                      const Text(
                        'GST Rate (%) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _gstRateController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter GST rate',
                          suffixText: '%',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: gstRateError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateGstRate(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'GST rate is required';
                          }
                          final double? gstRate = double.tryParse(value.trim());
                          if (gstRate == null || gstRate < 0 || gstRate > 100) {
                            return 'Please enter a valid GST rate (0-100)';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingService
                      ? null
                      : () async {
                          await _addService();
                          if (mounted && serviceNameError == null) {
                            Navigator.of(context).pop();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: isAddingService
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Add Service',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _editService(ServiceModel service) {
    _serviceNameController.text = service.name ?? '';
    _priceController.text = service.price?.toString() ?? '';
    _gstRateController.text = service.gstRate?.toString() ?? '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text(
                'Edit Service',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SizedBox(
                width: 400,
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Name Field
                      const Text(
                        'Service Name *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _serviceNameController,
                        decoration: InputDecoration(
                          hintText: 'Enter service name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: serviceNameError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateServiceName(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Service name is required';
                          }
                          if (value.trim().length < 2) {
                            return 'Service name must be at least 2 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Price Field
                      const Text(
                        'Price (MRP) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _priceController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter price',
                          prefixText: '₹ ',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: priceError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validatePrice(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Price is required';
                          }
                          final double? price = double.tryParse(value.trim());
                          if (price == null || price <= 0) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // GST Rate Field
                      const Text(
                        'GST Rate (%) *',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _gstRateController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: InputDecoration(
                          hintText: 'Enter GST rate',
                          suffixText: '%',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: gstRateError,
                        ),
                        onChanged: (value) {
                          setDialogState(() {
                            _validateGstRate(value);
                          });
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'GST rate is required';
                          }
                          final double? gstRate = double.tryParse(value.trim());
                          if (gstRate == null || gstRate < 0 || gstRate > 100) {
                            return 'Please enter a valid GST rate (0-100)';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingService
                      ? null
                      : () async {
                          await _updateService(service);
                          if (mounted && serviceNameError == null) {
                            Navigator.of(context).pop();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: isAddingService
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Update Service',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateService(ServiceModel service) async {
    if (!_formKey.currentState!.validate()) return;

    final serviceName = _serviceNameController.text.trim();
    final price = double.tryParse(_priceController.text.trim());
    final gstRate = double.tryParse(_gstRateController.text.trim());

    if (serviceName.isEmpty || price == null || gstRate == null) {
      _showErrorSnackBar('Please fill all required fields correctly');
      return;
    }

    setState(() {
      isAddingService = true;
    });

    try {
      // Check if service name already exists (case-insensitive) for other services
      if (serviceName.toLowerCase() != service.name?.toLowerCase()) {
        final bool exists = await _checkServiceNameExists(serviceName);
        if (exists) {
          setState(() {
            serviceNameError = 'Service with this name already exists';
            isAddingService = false;
          });
          return;
        }
      }

      final ServiceModel updatedService = ServiceModel(
        serviceId: service.serviceId,
        name: serviceName.toUpperCase(), // Store in uppercase
        price: price,
        gstRate: gstRate,
      );

      await FBFireStore.services
          .doc(service.serviceId)
          .update(updatedService.toMap());

      if (mounted) {
        setState(() {
          final index =
              allServices.indexWhere((s) => s.serviceId == service.serviceId);
          if (index != -1) {
            allServices[index] = updatedService;
          }
          _filterServices(_searchController.text);
          isAddingService = false;
        });

        _clearForm();
        _showSuccessSnackBar('Service updated successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingService = false;
        });
        _showErrorSnackBar('Error updating service: $e');
      }
    }
  }

  void _deleteService(ServiceModel service) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Delete Service',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Are you sure you want to delete this service?'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service: ${service.name}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('Price: ₹${service.price?.toStringAsFixed(2)}'),
                    Text('GST Rate: ${service.gstRate?.toStringAsFixed(1)}%'),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This action cannot be undone.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await FBFireStore.services.doc(service.serviceId).delete();

                  if (mounted) {
                    setState(() {
                      allServices
                          .removeWhere((s) => s.serviceId == service.serviceId);
                      _filterServices(_searchController.text);
                    });

                    Navigator.of(context).pop();
                    _showSuccessSnackBar('Service deleted successfully');
                  }
                } catch (e) {
                  if (mounted) {
                    Navigator.of(context).pop();
                    _showErrorSnackBar('Error deleting service: $e');
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text(
                'Delete',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
