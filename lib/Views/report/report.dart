import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/Views/report/widgets/report_card.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/router.dart';

class Report extends StatefulWidget {
  const Report({super.key});

  @override
  State<Report> createState() => _ReportState();
}

class _ReportState extends State<Report> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: PageHeaderWithButton(
            title: "Report",
            onPressed: () {},
            button: false,
          ),
        ),
        const SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: StaggeredGrid.extent(
            maxCrossAxisExtent: 150,
            mainAxisSpacing: 20,
            crossAxisSpacing: 20,
            children: [
              InkWell(
                  onTap: () async {
                    context.go(Routes.salesreport);
                  },
                  child: ReportCard(
                    icon: Icons.file_copy,
                    title: "Sales Report",
                  )),
              InkWell(
                  onTap: () async {
                    context.go(Routes.salesreport);
                  },
                  child: ReportCard(
                    icon: Icons.person_2,
                    title: "Customer Report",
                  )),
              InkWell(
                  onTap: () async {
                    context.go(Routes.salesreport);
                  },
                  child: ReportCard(
                    icon: Icons.person_remove_alt_1,
                    title: "Lost Customers Report",
                  )),
              InkWell(
                  onTap: () async {
                    context.go(Routes.salesreport);
                  },
                  child: ReportCard(
                    icon: Icons.file_open_sharp,
                    title: "MIS Report",
                  )),
              // InkWell(
              //     onTap: () {},
              //     child: ReportButton(
              //       icon: Icons.file_copy,
              //       title: "Customer Report",
              //     )),
            ],
          ),
        )
      ],
    );
  }
}
