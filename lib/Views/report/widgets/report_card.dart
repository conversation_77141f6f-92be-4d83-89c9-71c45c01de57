import 'package:flutter/material.dart';

class ReportCard extends StatelessWidget {
  const ReportCard({
    super.key,
    required this.title,
    required this.icon,
  });
  final String title;
  final IconData icon;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 3,
            blurRadius: 7,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(
            height: 20,
          ),
          Container(
            child: Icon(icon, size: 35, color: Colors.redAccent),
            padding: const EdgeInsets.all(12),
          ),
          Spacer(),
          Container(
            width: double.maxFinite,
            decoration: const BoxDecoration(
                color: Colors.redAccent,
                borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12))),
            child: Center(
                child: Text(
              overflow: TextOverflow.ellipsis,
              title,
              style: TextStyle(color: Colors.white),
            )),
            padding: const EdgeInsets.all(12),
          )
        ],
      ),
    );
  }
}
