import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';
import 'package:speed_force_admin/Views/report/widgets/report_card.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/makemodelsmodel.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/router.dart';

class SalesReport extends StatefulWidget {
  const SalesReport({super.key});

  @override
  State<SalesReport> createState() => _SalesReportState();
}

class _SalesReportState extends State<SalesReport> {
  String? selectedworkshop;
  List<CustomersModel> cusfilteredList3 = [];

  // Sales Report Data
  bool isLoading = false;
  List<RepairOrderModel> repairOrdersData = [];
  List<RepairOrderModel> searchedRepairOrdersData = [];
  DateTimeRange selectedDateRange = DateTimeRange(
      start: DateTime.utc(
              DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .subtract(const Duration(days: 30)),
      end: DateTime.utc(
          DateTime.now().year, DateTime.now().month, DateTime.now().day));

  List<String> headers = [
    "Sr No",
    "Date",
    "Invoice No",
    "Customer Name",
    "Vehicle",
    "Gross Value",
    "Discount",
    "Taxable Amount",
    "CGST",
    "SGST",
    "IGST",
    "Total",
    "Received Amount",
    "Payment Details"
  ];

  List<List<String>> rows = [];

  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {
      debugPrint('Error in getdplist: $e');
    }
  }

  Future<void> fetchSalesReport() async {
    try {
      setState(() {
        isLoading = true;
      });

      rows.clear();
      repairOrdersData.clear();

      QuerySnapshot<Map<String, dynamic>> ordersQuerySnapshot =
          await FirebaseFirestore.instance
              .collection('orders')
              .where("franchiseId", isEqualTo: selectedworkshop)
              .where('completionDate',
                  isGreaterThanOrEqualTo: selectedDateRange.start)
              .where('completionDate',
                  isLessThanOrEqualTo:
                      selectedDateRange.end.add(const Duration(days: 1)))
              .orderBy('completionDate', descending: true)
              .get();

      for (QueryDocumentSnapshot<Map<String, dynamic>> orderDoc
          in ordersQuerySnapshot.docs) {
        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromMap(orderDoc.id, orderDoc.data());

        repairOrdersData.add(repairOrderModel);
      }

      if (repairOrdersData.isNotEmpty) {
        generateDataForTable();
      }

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching sales report: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void generateDataForTable() {
    rows.clear();
    int index = 1;

    for (RepairOrderModel repairOrder in repairOrdersData) {
      double grossValue = 0.0;
      double tax = 0.0;

      // Calculate gross value and tax from parts
      for (var part in repairOrder.repairDetailsModel?.parts ?? []) {
        grossValue += part.purchasePrice ?? 0;
        tax += part.partsGst ?? 0;
      }

      // Calculate tax from services
      for (var service in repairOrder.repairDetailsModel?.services ?? []) {
        tax += service.servicesGst ?? 0;
      }

      List<String> rowData = [
        index.toString(),
        repairOrder.completionDate?.toDate().toString().split(' ')[0] ?? "NA",
        repairOrder.jobCardId?.toString() ?? "NA",
        repairOrder.customerDetailsModel?.username ?? "NA",
        "${repairOrder.vehicleDetailsModel?.make ?? ''} ${repairOrder.vehicleDetailsModel?.model ?? ''}"
            .trim(),
        grossValue == 0.0 ? "NA" : grossValue.toStringAsFixed(2),
        repairOrder.repairDetailsModel?.discount?.toStringAsFixed(2) ?? "NA",
        tax == 0.0 ? "NA" : tax.toStringAsFixed(2),
        // CGST
        (((repairOrder.gstIncluded ?? false) && !(repairOrder.isigst ?? false))
            ? (tax / 2).toStringAsFixed(2)
            : "NA"),
        // SGST
        (((repairOrder.gstIncluded ?? false) && !(repairOrder.isigst ?? false))
            ? (tax / 2).toStringAsFixed(2)
            : "NA"),
        // IGST
        (((repairOrder.gstIncluded ?? false) && (repairOrder.isigst ?? false))
            ? tax.toStringAsFixed(2)
            : "NA"),
        repairOrder.repairDetailsModel?.total?.toStringAsFixed(2) ?? "NA",
        repairOrder.repairDetailsModel?.paymentReceived?.toStringAsFixed(2) ??
            "NA",
        repairOrder.paymentMode?.name.toString() ?? "NA",
      ];

      rows.add(rowData);
      index++;
    }
  }

  void searchFromTable({required String searchTerm}) {
    if (searchTerm.isEmpty) {
      rows.clear();
      generateDataForTable();
      setState(() {});
    } else {
      searchedRepairOrdersData = repairOrdersData.where((order) {
        return (order.customerDetailsModel?.username
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) ||
            (order.jobCardId?.toString().contains(searchTerm) ?? false) ||
            (order.vehicleDetailsModel?.make
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) ||
            (order.vehicleDetailsModel?.model
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false);
      }).toList();

      rows.clear();
      int index = 1;

      for (RepairOrderModel repairOrder in searchedRepairOrdersData) {
        double grossValue = 0.0;
        double tax = 0.0;

        // Calculate values same as above
        for (var part in repairOrder.repairDetailsModel?.parts ?? []) {
          grossValue += part.purchasePrice ?? 0;
          tax += part.partsGst ?? 0;
        }

        for (var service in repairOrder.repairDetailsModel?.services ?? []) {
          tax += service.servicesGst ?? 0;
        }

        List<String> rowData = [
          index.toString(),
          repairOrder.completionDate?.toDate().toString().split(' ')[0] ?? "NA",
          repairOrder.jobCardId?.toString() ?? "NA",
          repairOrder.customerDetailsModel?.username ?? "NA",
          "${repairOrder.vehicleDetailsModel?.make ?? ''} ${repairOrder.vehicleDetailsModel?.model ?? ''}"
              .trim(),
          grossValue == 0.0 ? "NA" : grossValue.toStringAsFixed(2),
          repairOrder.repairDetailsModel?.discount?.toStringAsFixed(2) ?? "NA",
          tax == 0.0 ? "NA" : tax.toStringAsFixed(2),
          (((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (tax / 2).toStringAsFixed(2)
              : "NA"),
          (((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (tax / 2).toStringAsFixed(2)
              : "NA"),
          (((repairOrder.gstIncluded ?? false) && (repairOrder.isigst ?? false))
              ? tax.toStringAsFixed(2)
              : "NA"),
          repairOrder.repairDetailsModel?.total?.toStringAsFixed(2) ?? "NA",
          repairOrder.repairDetailsModel?.paymentReceived?.toStringAsFixed(2) ??
              "NA",
          repairOrder.paymentMode?.name.toString() ?? "NA",
        ];

        rows.add(rowData);
        index++;
      }

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 25,
            ),
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              isExpanded: true,
              focusColor: Colors.transparent,
              dropdownColor: Colors.white,
              decoration: InputDecoration(
                  hintText: "Select Workshop",
                  hintStyle: const TextStyle(fontSize: 30),
                  constraints:
                      const BoxConstraints(maxWidth: 300, maxHeight: 45),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5))),
              value: selectedworkshop,
              items: [
                ...List.generate(
                  ctrl.franchises.length,
                  (index) {
                    return DropdownMenuItem(
                        value: ctrl.franchises[index].docID,
                        child: Text(ctrl.franchises[index].garageName ?? ""));
                  },
                ),
              ],
              onChanged: (value) async {
                if (value == null) {
                  return;
                }
                selectedworkshop = value;
                fetchSalesReport();
                // getdplist(value);
              },
            )),
            SizedBox(
              height: 25,
            ),
            Container(
              padding: const EdgeInsets.all(8),
              // height: 14,
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 228, 228, 228),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                      width: 70,
                      child: Text('Sr No',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontWeight: FontWeight.w600))),
                  SizedBox(width: 20),
                  Expanded(child: TableHeaderText(headerName: 'CustomerName')),

                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Contact No.')),
                  // const SizedBox(width: 10),
                  // const SizedBox(
                  //     width: 150, child: TableHeaderText(headerName: 'Email')),
                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Make')),
                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Model')),
                  SizedBox(width: 10),
                  Expanded(
                      child: TableHeaderText(headerName: 'Last Service Date')),
                  SizedBox(width: 10),
                  Expanded(
                      child: TableHeaderText(headerName: 'Job Card Nummber')),
                  SizedBox(width: 10),
                ],
              ),
            ),
            SizedBox(
              height: 20,
            ),
            ...List.generate(cusfilteredList3.length, (index) {
              return SalesReportTile(
                  index: index, customersModel: cusfilteredList3[index]);
            }),
          ],
        ),
      );
    });
  }
}

class SalesReportTile extends StatefulWidget {
  const SalesReportTile(
      {super.key, required this.index, required this.customersModel});
  final int index;
  final CustomersModel customersModel;
  @override
  State<SalesReportTile> createState() => _SalesReportTileState();
}

class _SalesReportTileState extends State<SalesReportTile> {
  CustomerVehicleModel? makemodelsmodel;
  bool loading = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getMakeModel();
  }

  getMakeModel() async {
    try {
      final data = await FBFireStore.customersvehicles
          .where('customerId', isEqualTo: widget.customersModel.customerId)
          .limit(1)
          .get();
      if (data.docs.isNotEmpty) {
        makemodelsmodel = CustomerVehicleModel.fromSnap(data.docs.first);
      } else {
        makemodelsmodel = null;
      }
      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.grey[200],
        ),
        child: Row(
          children: [
            SizedBox(
                width: 70,
                child: Text((widget.index + 1).toString(),
                    textAlign: TextAlign.center)),
            const SizedBox(width: 20),
            Expanded(child: Text(widget.customersModel.username ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.phone ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.email ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(makemodelsmodel?.make ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(makemodelsmodel?.model ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.lastVisit ?? "")),
            const SizedBox(width: 10),
            Expanded(
                child: Text(
                    widget.customersModel.totalBillAmount.toString() ?? "")),
          ],
        ),
      ),
    );
  }
}
