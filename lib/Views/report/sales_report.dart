import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';
import 'package:speed_force_admin/Views/report/widgets/report_card.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/makemodelsmodel.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/router.dart';

class SalesReport extends StatefulWidget {
  const SalesReport({super.key});

  @override
  State<SalesReport> createState() => _SalesReportState();
}

class _SalesReportState extends State<SalesReport> {
  String? selectedworkshop;
  List<CustomersModel> cusfilteredList3 = [];

  // Sales Report Data
  bool isLoading = false;
  List<RepairOrderModel> repairOrdersData = [];
  List<RepairOrderModel> searchedRepairOrdersData = [];
  DateTimeRange selectedDateRange = DateTimeRange(
      start: DateTime.utc(
              DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .subtract(const Duration(days: 30)),
      end: DateTime.utc(
          DateTime.now().year, DateTime.now().month, DateTime.now().day));

  List<String> headers = [
    "Sr No",
    "Date",
    "Invoice No",
    "Job-card Number",
    "Customer Name",
    "Vehicle Number",
    "Make",
    "Model",
    "Service / Part Name",
    "Service / Part Category",
    "GST Number",
    "Gross Value",
    "Discount",
    "Taxable Amount",
    "Tax Rate",
    "CGST",
    "SGST",
    "IGST",
    "Total",
    "Received Amount",
    "Payment Details"
  ];

  List<List<String>> rows = [];

  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {
      debugPrint('Error in getdplist: $e');
    }
  }

  Future<void> fetchSalesReport() async {
    try {
      print('Starting fetchSalesReport...');

      setState(() {
        isLoading = true;
      });

      rows.clear();
      repairOrdersData.clear();

      QuerySnapshot<Map<String, dynamic>> ordersQuerySnapshot =
          await FirebaseFirestore.instance
              .collection('Orders')
              .where("franchiseId", isEqualTo: selectedworkshop)
              .where('completionDate',
                  isGreaterThanOrEqualTo: selectedDateRange.start)
              .where('completionDate',
                  isLessThanOrEqualTo:
                      selectedDateRange.end.add(const Duration(days: 1)))
              .orderBy('completionDate', descending: true)
              .get();

      print('Found ${ordersQuerySnapshot.docs.length} orders');

      for (QueryDocumentSnapshot<Map<String, dynamic>> orderDoc
          in ordersQuerySnapshot.docs) {
        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromMap(orderDoc.id, orderDoc.data());

        repairOrdersData.add(repairOrderModel);
      }

      print('Added ${repairOrdersData.length} repair orders to list');

      if (repairOrdersData.isNotEmpty) {
        generateDataForTable();
        print('Generated ${rows.length} table rows');
      } else {
        print('No repair orders found');
      }

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching sales report: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void generateDataForTable() {
    rows.clear();
    int srNo = 1;

    for (RepairOrderModel repairOrder in repairOrdersData) {
      // Get all parts and services for this repair order
      List<dynamic> allItems = [];
      allItems.addAll(repairOrder.repairDetailsModel?.parts ?? []);
      allItems.addAll(repairOrder.repairDetailsModel?.services ?? []);

      if (allItems.isEmpty) {
        // If no parts or services, create one row with basic info
        List<String> rowData = [
          srNo.toString(),
          repairOrder.completionDate?.toDate().toString().split(' ')[0] ?? "NA",
          repairOrder.invoiceId?.toString() ?? "NA",
          repairOrder.jobCardId?.toString() ?? "NA",
          repairOrder.customerDetailsModel?.username ?? "NA",
          repairOrder.vehicleDetailsModel?.registrationNumber ?? "NA",
          repairOrder.vehicleDetailsModel?.make ?? "NA",
          repairOrder.vehicleDetailsModel?.model ?? "NA",
          "No items",
          "NA",
          repairOrder.customerDetailsModel?.gstin ?? "NA",
          "0.00",
          repairOrder.repairDetailsModel?.discount?.toStringAsFixed(2) ??
              "0.00",
          "0.00",
          "0%",
          "0.00",
          "0.00",
          "0.00",
          repairOrder.repairDetailsModel?.total?.toStringAsFixed(2) ?? "0.00",
          repairOrder.repairDetailsModel?.paymentReceived?.toStringAsFixed(2) ??
              "0.00",
          repairOrder.paymentMode?.name.toString() ?? "NA",
        ];
        rows.add(rowData);
        srNo++;
      } else {
        // Create a row for each part/service
        for (var item in allItems) {
          double itemPrice = 0.0;
          double itemTax = 0.0;
          double taxRate = 0.0;
          String itemName = "";
          String category = "";

          if (item is ServicePartModel) {
            // Check if it's a part or service based on available fields
            if (item.purchasePrice != null && item.purchasePrice! > 0) {
              // It's a part
              itemPrice = item.purchasePrice ?? 0.0;
              itemTax = item.partsGst ?? 0.0;
              itemName = item.title ?? "Unknown Part";
              category = "Parts";
              taxRate = (item.gstRate ?? 0).toDouble();
            } else if (item.rate != null && item.rate! > 0) {
              // It's a service
              itemPrice = item.rate ?? 0.0;
              itemTax = item.servicesGst ?? 0.0;
              itemName = item.title ?? "Unknown Service";
              category = "Service";
              taxRate = (item.gstRate ?? 0).toDouble();
            } else {
              // Fallback - check which GST field has value
              if (item.partsGst != null && item.partsGst! > 0) {
                itemPrice = item.amount ?? 0.0;
                itemTax = item.partsGst ?? 0.0;
                itemName = item.title ?? "Unknown Part";
                category = "Parts";
                taxRate = (item.gstRate ?? 0).toDouble();
              } else {
                itemPrice = item.amount ?? 0.0;
                itemTax = item.servicesGst ?? 0.0;
                itemName = item.title ?? "Unknown Service";
                category = "Service";
                taxRate = (item.gstRate ?? 0).toDouble();
              }
            }
          }

          double taxableAmount = itemPrice;

          List<String> rowData = [
            srNo.toString(),
            repairOrder.completionDate?.toDate().toString().split(' ')[0] ??
                "NA",
            repairOrder.invoiceId?.toString() ?? "NA",
            repairOrder.jobCardId?.toString() ?? "NA",
            repairOrder.customerDetailsModel?.username ?? "NA",
            repairOrder.vehicleDetailsModel?.registrationNumber ?? "NA",
            repairOrder.vehicleDetailsModel?.make ?? "NA",
            repairOrder.vehicleDetailsModel?.model ?? "NA",
            itemName,
            category,
            repairOrder.customerDetailsModel?.gstin ?? "NA",
            itemPrice.toStringAsFixed(2),
            repairOrder.repairDetailsModel?.discount?.toStringAsFixed(2) ??
                "0.00",
            taxableAmount.toStringAsFixed(2),
            "${taxRate.toStringAsFixed(1)}%",
            // CGST
            (((repairOrder.gstIncluded ?? false) &&
                    !(repairOrder.isigst ?? false))
                ? (itemTax / 2).toStringAsFixed(2)
                : "0.00"),
            // SGST
            (((repairOrder.gstIncluded ?? false) &&
                    !(repairOrder.isigst ?? false))
                ? (itemTax / 2).toStringAsFixed(2)
                : "0.00"),
            // IGST
            (((repairOrder.gstIncluded ?? false) &&
                    (repairOrder.isigst ?? false))
                ? itemTax.toStringAsFixed(2)
                : "0.00"),
            (itemPrice + itemTax).toStringAsFixed(2),
            repairOrder.repairDetailsModel?.paymentReceived
                    ?.toStringAsFixed(2) ??
                "0.00",
            repairOrder.paymentMode?.name.toString() ?? "NA",
          ];

          rows.add(rowData);
          srNo++;
        }
      }
    }
  }

  void searchFromTable({required String searchTerm}) {
    if (searchTerm.isEmpty) {
      rows.clear();
      generateDataForTable();
      setState(() {});
    } else {
      searchedRepairOrdersData = repairOrdersData.where((order) {
        return (order.customerDetailsModel?.username
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) ||
            (order.jobCardId?.toString().contains(searchTerm) ?? false) ||
            (order.vehicleDetailsModel?.make
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) ||
            (order.vehicleDetailsModel?.model
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false);
      }).toList();

      rows.clear();
      int index = 1;

      for (RepairOrderModel repairOrder in searchedRepairOrdersData) {
        double grossValue = 0.0;
        double tax = 0.0;

        // Calculate values same as above
        for (var part in repairOrder.repairDetailsModel?.parts ?? []) {
          grossValue += part.purchasePrice ?? 0;
          tax += part.partsGst ?? 0;
        }

        for (var service in repairOrder.repairDetailsModel?.services ?? []) {
          tax += service.servicesGst ?? 0;
        }

        List<String> rowData = [
          index.toString(),
          repairOrder.completionDate?.toDate().toString().split(' ')[0] ?? "NA",
          repairOrder.jobCardId?.toString() ?? "NA",
          repairOrder.customerDetailsModel?.username ?? "NA",
          "${repairOrder.vehicleDetailsModel?.make ?? ''} ${repairOrder.vehicleDetailsModel?.model ?? ''}"
              .trim(),
          grossValue == 0.0 ? "NA" : grossValue.toStringAsFixed(2),
          repairOrder.repairDetailsModel?.discount?.toStringAsFixed(2) ?? "NA",
          tax == 0.0 ? "NA" : tax.toStringAsFixed(2),
          (((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (tax / 2).toStringAsFixed(2)
              : "NA"),
          (((repairOrder.gstIncluded ?? false) &&
                  !(repairOrder.isigst ?? false))
              ? (tax / 2).toStringAsFixed(2)
              : "NA"),
          (((repairOrder.gstIncluded ?? false) && (repairOrder.isigst ?? false))
              ? tax.toStringAsFixed(2)
              : "NA"),
          repairOrder.repairDetailsModel?.total?.toStringAsFixed(2) ?? "NA",
          repairOrder.repairDetailsModel?.paymentReceived?.toStringAsFixed(2) ??
              "NA",
          repairOrder.paymentMode?.name.toString() ?? "NA",
        ];

        rows.add(rowData);
        index++;
      }

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ctrl.franchises.sort((a, b) => (a.garageName?.toLowerCase().trim() ?? '')
          .compareTo(b.garageName?.toLowerCase().trim() ?? ''));
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 25,
              ),
              DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                isExpanded: true,
                focusColor: Colors.transparent,
                dropdownColor: Colors.white,
                decoration: InputDecoration(
                    hintText: "Select Workshop",
                    hintStyle: const TextStyle(fontSize: 30),
                    constraints:
                        const BoxConstraints(maxWidth: 300, maxHeight: 45),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5))),
                value: selectedworkshop,
                items: [
                  ...List.generate(
                    ctrl.franchises.length,
                    (index) {
                      return DropdownMenuItem(
                          value: ctrl.franchises[index].docID,
                          child: Text(ctrl.franchises[index].garageName ?? ""));
                    },
                  ),
                ],
                onChanged: (value) async {
                  if (value == null) {
                    return;
                  }
                  selectedworkshop = value;
                  fetchSalesReport();
                  // getdplist(value);
                },
              )),
              const SizedBox(
                height: 25,
              ),
              if (selectedworkshop != null) ...[
                Row(
                  children: [
                    SizedBox(
                      width: 600,
                      child: TextField(
                        decoration: const InputDecoration(
                          hintText:
                              'Search by customer name, invoice, vehicle...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          searchFromTable(searchTerm: value);
                        },
                      ),
                    ),
                    Spacer(),
                    ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor: const WidgetStatePropertyAll(
                              Color.fromARGB(255, 228, 60, 60)),
                          padding: const WidgetStatePropertyAll(
                              EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 20)),
                          side: const WidgetStatePropertyAll(
                              BorderSide(color: Colors.transparent)),
                          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ))),
                      onPressed: () async {
                        DateTimeRange? picked = await showDateRangePicker(
                          context: context,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                          initialDateRange: selectedDateRange,
                        );
                        if (picked != null) {
                          selectedDateRange = picked;
                          if (selectedworkshop != null) {
                            fetchSalesReport();
                          }
                        }
                      },
                      child: Text(
                        '${selectedDateRange.start.toString().split(' ')[0]} - ${selectedDateRange.end.toString().split(' ')[0]}',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 25,
                ),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    width: headers.length * 120.0, // Fixed width per column
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(255, 228, 228, 228),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: headers.map((header) {
                        return SizedBox(
                          width: 120, // Fixed width for each column
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: Text(
                              header,
                              textAlign: TextAlign.center,
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                if (isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (rows.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: Text(
                        'No sales data found for the selected date range',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ),
                  )
                else
                  ...List.generate(rows.length, (index) {
                    return SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        width: headers.length * 120.0, // Same width as header
                        padding: const EdgeInsets.all(8),
                        margin: const EdgeInsets.only(bottom: 2),
                        decoration: BoxDecoration(
                          color:
                              index % 2 == 0 ? Colors.grey[100] : Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: rows[index].map((cellData) {
                            return SizedBox(
                              width: 120, // Fixed width for each cell
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 5),
                                child: Text(
                                  cellData,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    );
                  }),
              ]
            ],
          ),
        ),
      );
    });
  }
}

class SalesReportTile extends StatefulWidget {
  const SalesReportTile(
      {super.key, required this.index, required this.customersModel});
  final int index;
  final CustomersModel customersModel;
  @override
  State<SalesReportTile> createState() => _SalesReportTileState();
}

class _SalesReportTileState extends State<SalesReportTile> {
  CustomerVehicleModel? makemodelsmodel;
  bool loading = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getMakeModel();
  }

  getMakeModel() async {
    try {
      final data = await FBFireStore.customersvehicles
          .where('customerId', isEqualTo: widget.customersModel.customerId)
          .limit(1)
          .get();
      if (data.docs.isNotEmpty) {
        makemodelsmodel = CustomerVehicleModel.fromSnap(data.docs.first);
      } else {
        makemodelsmodel = null;
      }
      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.grey[200],
        ),
        child: Row(
          children: [
            SizedBox(
                width: 70,
                child: Text((widget.index + 1).toString(),
                    textAlign: TextAlign.center)),
            const SizedBox(width: 20),
            Expanded(child: Text(widget.customersModel.username ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.phone ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.email ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(makemodelsmodel?.make ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(makemodelsmodel?.model ?? "")),
            const SizedBox(width: 10),
            Expanded(child: Text(widget.customersModel.lastVisit ?? "")),
            const SizedBox(width: 10),
            Expanded(
                child: Text(
                    widget.customersModel.totalBillAmount.toString() ?? "")),
          ],
        ),
      ),
    );
  }
}
