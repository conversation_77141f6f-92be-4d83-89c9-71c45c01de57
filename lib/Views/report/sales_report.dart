import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/report/widgets/report_card.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/router.dart';

class SalesReport extends StatefulWidget {
  const SalesReport({super.key});

  @override
  State<SalesReport> createState() => _SalesReportState();
}

class _SalesReportState extends State<SalesReport> {
  String? selectedworkshop;
  List<CustomersModel> cusfilteredList3 = [];
  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {}
  }
  // Future<void> fetchTotalCustomersReport() async {
  //   try {
  //     isLoading = true;
  //     setState(() {

  //     });

  //     QuerySnapshot<Map<String, dynamic>> customersQuerySnapshot =
  //         lastVisible == null
  //             ? await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.customers.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .limit(25)
  //                 .get()
  //             : await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.customers.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .startAfter([lastVisible])
  //                 .limit(25)
  //                 .get();

  //     lastVisible =
  //         customersQuerySnapshot.docs[customersQuerySnapshot.docs.length - 1];

  //     for (QueryDocumentSnapshot<
  //             Map<String, dynamic>> customerQueryDocumentSnapshot
  //         in customersQuerySnapshot.docs) {
  //       CustomerDetailsModel customerDetailsModel =
  //           CustomerDetailsModel.fromMap(customerQueryDocumentSnapshot.data());

  //       CustomerAllDetailsModel customerAllDetailsModel =
  //           CustomerAllDetailsModel(
  //               customerDetailsModel: customerDetailsModel,
  //               vehicleAndRepairOrdersMap: {});

  //       QuerySnapshot<Map<String, dynamic>> customerVehiclesQuerySnapshot =
  //           await FirebaseFirestore.instance
  //               .collection(FirebaseCollections.customerVehicles.name)
  //               .where("franchiseId",
  //                   isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //               .where("customerId", isEqualTo: customerDetailsModel.customerId)
  //               .get();

  //       for (QueryDocumentSnapshot<
  //               Map<String, dynamic>> customerVehiclesQueryDocumentSnapshot
  //           in customerVehiclesQuerySnapshot.docs) {
  //         VehicleDetailsModel vehicleDetailsModel = VehicleDetailsModel.fromMap(
  //             customerVehiclesQueryDocumentSnapshot.data());

  //         customerAllDetailsModel
  //             .vehicleAndRepairOrdersMap?[vehicleDetailsModel] = [];

  //         QuerySnapshot<Map<String, dynamic>> repairOrderQuerySnapshot =
  //             await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.orders.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .where("vehicleId", isEqualTo: vehicleDetailsModel.vehicleId)
  //                 .get();

  //         for (QueryDocumentSnapshot<
  //                 Map<String, dynamic>> repairQueryDocumentSnapshot
  //             in repairOrderQuerySnapshot.docs) {
  //           RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
  //               repairQueryDocumentSnapshot.id,
  //               repairQueryDocumentSnapshot.data());

  //           customerAllDetailsModel
  //               .vehicleAndRepairOrdersMap?[vehicleDetailsModel]
  //               ?.add(repairOrderModel);
  //         }
  //       }
  //       customersData.add(customerAllDetailsModel);
  //     }

  //     // if (customersData.isNotEmpty) {
  //     //   generateDataForCSV(customerAllDetailsModels: customersData);
  //     // }

  //     isLoading = false;
  //     setState(() {

  //     });();
  //   } catch (e) {
  //     isLoading = false;
  //     setState(() {

  //     });();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 25,
            ),
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              isExpanded: true,
              focusColor: Colors.transparent,
              dropdownColor: Colors.white,
              decoration: InputDecoration(
                  hintText: "Select Workshop",
                  hintStyle: const TextStyle(fontSize: 30),
                  constraints:
                      const BoxConstraints(maxWidth: 300, maxHeight: 45),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5))),
              value: selectedworkshop,
              items: [
                ...List.generate(
                  ctrl.franchises.length,
                  (index) {
                    return DropdownMenuItem(
                        value: ctrl.franchises[index].docID,
                        child: Text(ctrl.franchises[index].garageName ?? ""));
                  },
                ),
              ],
              onChanged: (value) async {
                if (value == null) {
                  return;
                }
                selectedworkshop = value;
                // getdplist(value);
              },
            )),
          ],
        ),
      );
    });
  }
}
