import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/common/table_header.dart';
import 'package:speed_force_admin/Views/report/widgets/report_card.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_admin/models/customer_vehicles.dart';
import 'package:speed_force_admin/models/customers_model.dart';
import 'package:speed_force_admin/models/makemodelsmodel.dart';
import 'package:speed_force_admin/models/repair_order_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/shared/router.dart';

class SalesReport extends StatefulWidget {
  const SalesReport({super.key});

  @override
  State<SalesReport> createState() => _SalesReportState();
}

class _SalesReportState extends State<SalesReport> {
  String? selectedworkshop;
  List<CustomersModel> cusfilteredList3 = [];
  getdplist(String value) async {
    try {
      cusfilteredList3 = (await FBFireStore.customers
              .where('franchiseId', isEqualTo: selectedworkshop)
              .get())
          .docs
          .map((e) => CustomersModel.fromSnap(e))
          .toList();

      setState(() {});
      print('>>>>>>> ${cusfilteredList3.length}');
    } catch (e) {}
  }
  // Future<void> fetchTotalCustomersReport() async {
  //   try {
  //     isLoading = true;
  //     setState(() {

  //     });

  //     QuerySnapshot<Map<String, dynamic>> customersQuerySnapshot =
  //         lastVisible == null
  //             ? await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.customers.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .limit(25)
  //                 .get()
  //             : await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.customers.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .startAfter([lastVisible])
  //                 .limit(25)
  //                 .get();

  //     lastVisible =
  //         customersQuerySnapshot.docs[customersQuerySnapshot.docs.length - 1];

  //     for (QueryDocumentSnapshot<
  //             Map<String, dynamic>> customerQueryDocumentSnapshot
  //         in customersQuerySnapshot.docs) {
  //       CustomerDetailsModel customerDetailsModel =
  //           CustomerDetailsModel.fromMap(customerQueryDocumentSnapshot.data());

  //       CustomerAllDetailsModel customerAllDetailsModel =
  //           CustomerAllDetailsModel(
  //               customerDetailsModel: customerDetailsModel,
  //               vehicleAndRepairOrdersMap: {});

  //       QuerySnapshot<Map<String, dynamic>> customerVehiclesQuerySnapshot =
  //           await FirebaseFirestore.instance
  //               .collection(FirebaseCollections.customerVehicles.name)
  //               .where("franchiseId",
  //                   isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //               .where("customerId", isEqualTo: customerDetailsModel.customerId)
  //               .get();

  //       for (QueryDocumentSnapshot<
  //               Map<String, dynamic>> customerVehiclesQueryDocumentSnapshot
  //           in customerVehiclesQuerySnapshot.docs) {
  //         VehicleDetailsModel vehicleDetailsModel = VehicleDetailsModel.fromMap(
  //             customerVehiclesQueryDocumentSnapshot.data());

  //         customerAllDetailsModel
  //             .vehicleAndRepairOrdersMap?[vehicleDetailsModel] = [];

  //         QuerySnapshot<Map<String, dynamic>> repairOrderQuerySnapshot =
  //             await FirebaseFirestore.instance
  //                 .collection(FirebaseCollections.orders.name)
  //                 .where("franchiseId",
  //                     isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
  //                 .where("vehicleId", isEqualTo: vehicleDetailsModel.vehicleId)
  //                 .get();

  //         for (QueryDocumentSnapshot<
  //                 Map<String, dynamic>> repairQueryDocumentSnapshot
  //             in repairOrderQuerySnapshot.docs) {
  //           RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
  //               repairQueryDocumentSnapshot.id,
  //               repairQueryDocumentSnapshot.data());

  //           customerAllDetailsModel
  //               .vehicleAndRepairOrdersMap?[vehicleDetailsModel]
  //               ?.add(repairOrderModel);
  //         }
  //       }
  //       customersData.add(customerAllDetailsModel);
  //     }

  //     // if (customersData.isNotEmpty) {
  //     //   generateDataForCSV(customerAllDetailsModels: customersData);
  //     // }

  //     isLoading = false;
  //     setState(() {

  //     });();
  //   } catch (e) {
  //     isLoading = false;
  //     setState(() {

  //     });();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 25,
            ),
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              isExpanded: true,
              focusColor: Colors.transparent,
              dropdownColor: Colors.white,
              decoration: InputDecoration(
                  hintText: "Select Workshop",
                  hintStyle: const TextStyle(fontSize: 30),
                  constraints:
                      const BoxConstraints(maxWidth: 300, maxHeight: 45),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5))),
              value: selectedworkshop,
              items: [
                ...List.generate(
                  ctrl.franchises.length,
                  (index) {
                    return DropdownMenuItem(
                        value: ctrl.franchises[index].docID,
                        child: Text(ctrl.franchises[index].garageName ?? ""));
                  },
                ),
              ],
              onChanged: (value) async {
                if (value == null) {
                  return;
                }
                selectedworkshop = value;
                // getdplist(value);
              },
            )),
            SizedBox(
              height: 25,
            ),
            Container(
              padding: const EdgeInsets.all(8),
              // height: 14,
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 228, 228, 228),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                      width: 70,
                      child: Text('Sr No',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontWeight: FontWeight.w600))),
                  SizedBox(width: 20),
                  Expanded(child: TableHeaderText(headerName: 'CustomerName')),

                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Contact No.')),
                  // const SizedBox(width: 10),
                  // const SizedBox(
                  //     width: 150, child: TableHeaderText(headerName: 'Email')),
                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Make')),
                  SizedBox(width: 10),
                  Expanded(child: TableHeaderText(headerName: 'Model')),
                  SizedBox(width: 10),
                  Expanded(
                      child: TableHeaderText(headerName: 'Last Service Date')),
                  SizedBox(width: 10),
                  Expanded(
                      child: TableHeaderText(headerName: 'Job Card Nummber')),
                  SizedBox(width: 10),
                ],
              ),
            ),
            SizedBox(
              height: 20,
            ),
            ...List.generate(cusfilteredList3.length, (index) {
              return SalesReportTile(index: index, customersModel: cusfilteredList3[index]);
            }),
          ],
        ),
      );
    });
  }
}
class SalesReportTile extends StatefulWidget {
  const SalesReportTile({super.key, required this.index, required this.customersModel});
final int index;
final CustomersModel customersModel;
  @override
  State<SalesReportTile> createState() => _SalesReportTileState();
}

class _SalesReportTileState extends State<SalesReportTile> {
  CustomerVehicleModel? makemodelsmodel;
  bool loading = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getMakeModel();
  }
  getMakeModel() async {
    try {
      final data = await FBFireStore.customersvehicles
          .where(field)
          .get();
      if (data.data() != null) {
        makemodelsmodel = CustomerVehicleModel.fromSnap(data);
      } else {
        makemodelsmodel = null;
      }
      setState(() {});
    } catch (e) {
      debugPrint(e.toString());
    }
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
                padding: const EdgeInsets.only(bottom: 10.0),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[200],
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                          width: 70,
                          child: Text((widget.index + 1).toString(),
                              textAlign: TextAlign.center)),
                      const SizedBox(width: 20),
                      Expanded(
                          child: Text(widget.customersModel.username ?? "")),
                      const SizedBox(width: 10),
                      Expanded(
                          child: Text(widget.customersModel.phone ?? "")),
                      const SizedBox(width: 10),
                      Expanded(
                          child: Text(widget.customersModel.email ?? "")),
                      const SizedBox(width: 10),
                      Expanded(child: Text(cusfilteredList3[index].make ?? "")),
                      const SizedBox(width: 10),
                      Expanded(
                          child: Text(cusfilteredList3[index].model ?? "")),
                      const SizedBox(width: 10),
                      Expanded(
                          child: Text(widget.customersModel.lastVisit ?? "")),
                      const SizedBox(width: 10),
                      Expanded(
                          child: Text(widget.customersModel
                                  .totalBillAmount
                                  .toString() ??
                              "")),
                    ],
                  ),
                ),
              );
            
  }
}