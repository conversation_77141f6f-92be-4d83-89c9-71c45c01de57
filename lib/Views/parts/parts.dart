import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/models/part_model.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import 'package:speed_force_admin/Views/common/page_header.dart';
import 'package:speed_force_admin/shared/methods.dart';

class Parts extends StatefulWidget {
  const Parts({super.key});

  @override
  State<Parts> createState() => _PartsState();
}

class _PartsState extends State<Parts> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _partNameController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  final TextEditingController _manufacturerController = TextEditingController();
  final TextEditingController _mrpController = TextEditingController();
  final TextEditingController _purchasePriceController =
      TextEditingController();
  final TextEditingController _gstRateController = TextEditingController();

  List<PartModel> allParts = [];
  List<PartModel> filteredParts = [];
  bool isLoading = false;
  bool isAddingPart = false;
  Timer? _debounceTimer;

  // Form validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Category dropdown
  String? selectedCategory;

  @override
  void initState() {
    super.initState();
    _fetchParts();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _partNameController.dispose();
    _categoryController.dispose();
    _manufacturerController.dispose();
    _mrpController.dispose();
    _purchasePriceController.dispose();
    _gstRateController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _filterParts(_searchController.text);
    });
  }

  void _filterParts(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredParts = List.from(allParts);
      } else {
        filteredParts = allParts.where((part) {
          return part.name!.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _fetchParts() async {
    if (!mounted) return;
    setState(() {
      isLoading = true;
    });

    try {
      final QuerySnapshot snapshot = await FBFireStore.parts
          .where('franchiseId', isEqualTo: null)
          .orderBy('name')
          .get();

      if (mounted) {
        setState(() {
          // Filter to show only admin parts (franchiseId is null)
          allParts = snapshot.docs
              .map((doc) =>
                  PartModel.fromMap(doc.data() as Map<String, dynamic>))
              .where((part) => part.franchiseId == null)
              .toList();
          filteredParts = List.from(allParts);
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Error fetching parts: $e');
      }
    }
  }

  Future<bool> _checkPartNameExists(String partName) async {
    try {
      final QuerySnapshot snapshot = await FBFireStore.parts.get();

      // Check for case-insensitive duplicate names among admin parts only (franchiseId is null)
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final existingName = data['name']?.toString().toLowerCase() ?? '';
        final franchiseId = data['franchiseId'];

        // Only check admin parts (franchiseId is null)
        if (franchiseId == null && existingName == partName.toLowerCase()) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Form validators
  String? _validatePartName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Part name is required';
    }
    if (value.length < 2) {
      return 'Part name must be at least 2 characters long';
    }
    if (value.length > 100) {
      return 'Part name must be less than 100 characters';
    }
    return null;
  }

  String? _validateCategory(String? value) {
    if (value == null || value.isEmpty) {
      return 'Category is required';
    }
    return null;
  }

  String? _validateManufacturer(String? value) {
    if (value == null || value.isEmpty) {
      return 'Manufacturer is required';
    }
    if (value.length < 2) {
      return 'Manufacturer name must be at least 2 characters long';
    }
    if (value.length > 50) {
      return 'Manufacturer name must be less than 50 characters';
    }
    return null;
  }

  String? _validateMrp(String? value) {
    if (value == null || value.isEmpty) {
      return 'MRP is required';
    }
    final double? mrp = double.tryParse(value);
    if (mrp == null || mrp <= 0) {
      return 'Please enter a valid MRP greater than 0';
    }
    if (mrp > 999999) {
      return 'MRP cannot exceed 999,999';
    }
    return null;
  }

  String? _validatePurchasePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Purchase price is required';
    }
    final double? price = double.tryParse(value);
    if (price == null || price <= 0) {
      return 'Please enter a valid purchase price greater than 0';
    }
    if (price > 999999) {
      return 'Purchase price cannot exceed 999,999';
    }
    return null;
  }

  String? _validateGstRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'GST rate is required';
    }
    final double? gstRate = double.tryParse(value);
    if (gstRate == null || gstRate < 0 || gstRate > 100) {
      return 'Please enter a valid GST rate (0-100)';
    }
    return null;
  }

  // Business logic validation
  String? _validateBusinessLogic() {
    final String mrpText = _mrpController.text.trim();
    final String purchasePriceText = _purchasePriceController.text.trim();

    if (mrpText.isNotEmpty && purchasePriceText.isNotEmpty) {
      final double? mrp = double.tryParse(mrpText);
      final double? purchasePrice = double.tryParse(purchasePriceText);

      if (mrp != null && purchasePrice != null && mrp < purchasePrice) {
        return 'MRP cannot be less than Purchase Price';
      }
    }
    return null;
  }

  Future<void> _addPart() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Business logic validation
    final String? businessError = _validateBusinessLogic();
    if (businessError != null) {
      showAppSnackbar(businessError);
      return;
    }

    final String partName = _partNameController.text.trim();
    final String manufacturer = _manufacturerController.text.trim();
    final String mrpText = _mrpController.text.trim();
    final String purchasePriceText = _purchasePriceController.text.trim();
    final String gstRateText = _gstRateController.text.trim();

    final double? mrp = double.tryParse(mrpText);
    final double? purchasePrice = double.tryParse(purchasePriceText);
    final double? gstRate = double.tryParse(gstRateText);

    if (mrp == null || purchasePrice == null || gstRate == null) {
      showAppSnackbar('Please enter valid numeric values');
      return;
    }

    // Additional business logic validation
    if (mrp < purchasePrice) {
      showAppSnackbar('MRP cannot be less than Purchase Price');
      return;
    }

    if (partName.length < 2) {
      showAppSnackbar('Part name must be at least 2 characters long');
      return;
    }

    if (manufacturer.length < 2) {
      showAppSnackbar('Manufacturer name must be at least 2 characters long');
      return;
    }

    setState(() {
      isAddingPart = true;
    });

    try {
      // Check if part name already exists (case-insensitive)
      final bool exists = await _checkPartNameExists(partName);
      if (exists) {
        setState(() {
          isAddingPart = false;
        });
        showAppSnackbar('Part with this name already exists');
        return;
      }

      final String partId = FBFireStore.parts.doc().id;
      final PartModel newPart = PartModel(
        partId: partId,
        name: partName.toUpperCase(), // Store in uppercase
        category: selectedCategory,
        manufacturer: manufacturer,
        mrp: mrp,
        purchasePrice: purchasePrice,
        gstRate: gstRate,
      );

      await FBFireStore.parts.doc(partId).set(newPart.toMap());

      if (mounted) {
        setState(() {
          allParts.add(newPart);
          _filterParts(_searchController.text);
          isAddingPart = false;
        });

        _clearForm();
        _showSuccessSnackBar('Part added successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingPart = false;
        });
        _showErrorSnackBar('Error adding part: $e');
      }
    }
  }

  void _clearForm() {
    _formKey.currentState?.reset();
    _partNameController.clear();
    _categoryController.clear();
    _manufacturerController.clear();
    _mrpController.clear();
    _purchasePriceController.clear();
    _gstRateController.clear();
    setState(() {
      selectedCategory = null;
    });
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: PageHeaderWithButton(
            title: "Parts",
            onPressed: () => _showAddPartDialog(),
            button: true,
            buttonName: "Add Part",
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 450),
            child: SizedBox(
              height: 45,
              child: TextFormField(
                controller: _searchController,
                cursorHeight: 20,
                onChanged: (value) {
                  _onSearchChanged();
                },
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(),
                  fillColor: Colors.grey.shade100,
                  filled: true,
                  prefixIcon: const Icon(
                    Icons.search,
                    size: 22,
                    color: Colors.blue,
                  ),
                  border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: BorderRadius.circular(7)),
                  hintText: 'Search parts...',
                  hintStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
        //   child: TextField(
        //     controller: _searchController,
        //     decoration: InputDecoration(
        //       hintText: 'Search parts...',
        //       prefixIcon: const Icon(Icons.search),
        //       border: OutlineInputBorder(
        //         borderRadius: BorderRadius.circular(8),
        //       ),
        //     ),
        //   ),
        // ),
        const SizedBox(height: 20),
        Expanded(
          child: isLoading
              ? const Center(child: CircularProgressIndicator())
              : filteredParts.isEmpty
                  ? const Center(
                      child: Text(
                        'No parts found',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Column(
                        children: [
                          // Table Header
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8),
                              ),
                            ),
                            child: const Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Text(
                                    'Sr No',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    'Part Name',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'Category',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'Manufacturer',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'MRP (₹)',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'Purchase Price (₹)',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'GST Rate (%)',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'Actions',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Table Body
                          Expanded(
                            child: ListView.builder(
                              itemCount: filteredParts.length,
                              itemBuilder: (context, index) {
                                final part = filteredParts[index];
                                return Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom:
                                          BorderSide(color: Colors.grey[300]!),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 1,
                                        child: Text(
                                          '${index + 1}',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 3,
                                        child: Text(
                                          part.name ?? 'N/A',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          part.category ?? 'N/A',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          part.manufacturer ?? 'N/A',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          '₹${part.mrp?.toStringAsFixed(2) ?? '0.00'}',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          '₹${part.purchasePrice?.toStringAsFixed(2) ?? '0.00'}',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          '${part.gstRate?.toStringAsFixed(1) ?? '0.0'}%',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Row(
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.edit,
                                                  color: Colors.blue),
                                              onPressed: () =>
                                                  _showEditPartDialog(part),
                                              tooltip: 'Edit',
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete,
                                                  color: Colors.red),
                                              onPressed: () =>
                                                  _showDeleteConfirmation(part),
                                              tooltip: 'Delete',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
        ),
      ],
    );
  }

  void _showAddPartDialog() {
    _clearForm();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.white,
              title: const Text('Add New Part'),
              content: SizedBox(
                width: 500,
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextFormField(
                          controller: _partNameController,
                          decoration: const InputDecoration(
                            labelText: 'Part Name *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _validatePartName,
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'Category *',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(
                              value: 'spares',
                              child: Text('Spares'),
                            ),
                            DropdownMenuItem(
                              value: 'lubes',
                              child: Text('Lubes'),
                            ),
                          ],
                          validator: _validateCategory,
                          onChanged: (String? value) {
                            setState(() {
                              selectedCategory = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _manufacturerController,
                          decoration: const InputDecoration(
                            labelText: 'Manufacturer *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _validateManufacturer,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _mrpController,
                          decoration: const InputDecoration(
                            labelText: 'MRP (₹) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validateMrp,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _purchasePriceController,
                          decoration: const InputDecoration(
                            labelText: 'Purchase Price (₹) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validatePurchasePrice,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _gstRateController,
                          decoration: const InputDecoration(
                            labelText: 'GST Rate (%) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validateGstRate,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingPart
                      ? null
                      : () async {
                          await _addPart();
                          if (mounted) {
                            Navigator.of(context).pop();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: isAddingPart
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Add Part'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditPartDialog(PartModel part) {
    _partNameController.text = part.name ?? '';
    selectedCategory = part.category;
    _manufacturerController.text = part.manufacturer ?? '';
    _mrpController.text = part.mrp?.toString() ?? '';
    _purchasePriceController.text = part.purchasePrice?.toString() ?? '';
    _gstRateController.text = part.gstRate?.toString() ?? '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Edit Part'),
              content: SizedBox(
                width: 500,
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextFormField(
                          controller: _partNameController,
                          decoration: const InputDecoration(
                            labelText: 'Part Name *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _validatePartName,
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'Category *',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(
                              value: 'spares',
                              child: Text('Spares'),
                            ),
                            DropdownMenuItem(
                              value: 'lubes',
                              child: Text('Lubes'),
                            ),
                          ],
                          validator: _validateCategory,
                          onChanged: (String? value) {
                            setState(() {
                              selectedCategory = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _manufacturerController,
                          decoration: const InputDecoration(
                            labelText: 'Manufacturer *',
                            border: OutlineInputBorder(),
                          ),
                          validator: _validateManufacturer,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _mrpController,
                          decoration: const InputDecoration(
                            labelText: 'MRP (₹) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validateMrp,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _purchasePriceController,
                          decoration: const InputDecoration(
                            labelText: 'Purchase Price (₹) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validatePurchasePrice,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _gstRateController,
                          decoration: const InputDecoration(
                            labelText: 'GST Rate (%) *',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: _validateGstRate,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _clearForm();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isAddingPart
                      ? null
                      : () async {
                          await _updatePart(part);
                          if (mounted) {
                            Navigator.of(context).pop();
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: isAddingPart
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Update Part'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updatePart(PartModel part) async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Business logic validation
    final String? businessError = _validateBusinessLogic();
    if (businessError != null) {
      showAppSnackbar(businessError);
      return;
    }

    final String partName = _partNameController.text.trim();
    final String manufacturer = _manufacturerController.text.trim();
    final String mrpText = _mrpController.text.trim();
    final String purchasePriceText = _purchasePriceController.text.trim();
    final String gstRateText = _gstRateController.text.trim();

    final double? mrp = double.tryParse(mrpText);
    final double? purchasePrice = double.tryParse(purchasePriceText);
    final double? gstRate = double.tryParse(gstRateText);

    if (mrp == null || purchasePrice == null || gstRate == null) {
      showAppSnackbar('Please enter valid numeric values');
      return;
    }

    // Additional business logic validation
    if (mrp < purchasePrice) {
      showAppSnackbar('MRP cannot be less than Purchase Price');
      return;
    }

    if (partName.length < 2) {
      showAppSnackbar('Part name must be at least 2 characters long');
      return;
    }

    if (manufacturer.length < 2) {
      showAppSnackbar('Manufacturer name must be at least 2 characters long');
      return;
    }

    setState(() {
      isAddingPart = true;
    });

    try {
      // Check if part name already exists (case-insensitive) excluding current part
      if (partName.toLowerCase() != part.name?.toLowerCase()) {
        final bool exists = await _checkPartNameExists(partName);
        if (exists) {
          setState(() {
            isAddingPart = false;
          });
          showAppSnackbar('Part with this name already exists');
          return;
        }
      }

      final PartModel updatedPart = PartModel(
        partId: part.partId,
        name: partName.toUpperCase(), // Store in uppercase
        category: selectedCategory,
        manufacturer: manufacturer,
        mrp: mrp,
        purchasePrice: purchasePrice,
        gstRate: gstRate,
      );

      await FBFireStore.parts.doc(part.partId).update(updatedPart.toMap());

      if (mounted) {
        setState(() {
          final index = allParts.indexWhere((p) => p.partId == part.partId);
          if (index != -1) {
            allParts[index] = updatedPart;
          }
          _filterParts(_searchController.text);
          isAddingPart = false;
        });

        _clearForm();
        _showSuccessSnackBar('Part updated successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isAddingPart = false;
        });
        _showErrorSnackBar('Error updating part: $e');
      }
    }
  }

  void _showDeleteConfirmation(PartModel part) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Part'),
          content: Text('Are you sure you want to delete "${part.name}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deletePart(part);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deletePart(PartModel part) async {
    try {
      await FBFireStore.parts.doc(part.partId).delete();

      if (mounted) {
        setState(() {
          allParts.removeWhere((p) => p.partId == part.partId);
          _filterParts(_searchController.text);
        });

        _showSuccessSnackBar('Part deleted successfully');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error deleting part: $e');
      }
    }
  }
}
