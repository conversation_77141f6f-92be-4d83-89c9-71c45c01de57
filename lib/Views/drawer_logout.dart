import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/shared/firebase.dart';
import '../shared/router.dart';

class DrawerLogout extends StatelessWidget {
  const DrawerLogout({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        onPressed: () => showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
                  title: const Text('Alert'),
                  content: const Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                        onPressed: () async {
                          await FBAuth.auth.signOut();
                          if (context.mounted) context.go(Routes.auth);
                        },
                        child: const Text('Yes')),
                    TextButton(
                        onPressed: () => context.pop(),
                        child: const Text('No')),
                  ],
                )),
        style: ElevatedButton.styleFrom(
          shadowColor: Colors.transparent,
          overlayColor: Colors.transparent,
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        ),
        child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(CupertinoIcons.power, size: 22),
                Padding(
                  padding: EdgeInsets.only(top: 5.0),
                  child: Text('Logout'),
                ),
              ],
            )));
  }
}
