abstract class IAppAssetsConstants {
  factory IAppAssetsConstants.getInstance() => AppAssetsConstants();

  String get basePath;
  String get icons;
  String get iconsSvg;
  String get images;
  String get imagesSvg;

  String get noImagePlaceholder;
  String get logo;

  String get completedOrder;
  String get createInvoice;
  String get createRepairOrder;
  String get openOrders;
  String get parts;
  String get paymentDue;
  String get readyOrder;
  String get wipOrder;
  String get profilePlaceholder;

  String get totalCustomerReport;
  String get monthlyCustomerReport;
  String get pieChart;
  String get sparePartsConsumption;
  String get businessComparison;
  String get monthlyDetailedMISReport;
  String get lostCustomerData;
  String get inventoryReport;
  String get customerSalesReport;
}

class AppAssetsConstants implements IAppAssetsConstants {
  @override
  String get basePath => "assets";

  @override
  String get icons => "$basePath/icons";

  @override
  String get iconsSvg => "$icons/svg";

  @override
  String get images => "$basePath/images";

  @override
  String get imagesSvg => "$images/svgs";

  @override
  String get noImagePlaceholder => "$imagesSvg/no_image_placeholder.svg";

  @override
  String get logo => "$images/logo.png";

  @override
  String get completedOrder => "$imagesSvg/completed_orders.svg";

  @override
  String get createInvoice => "$imagesSvg/create_invoice.svg";

  @override
  String get createRepairOrder => "$imagesSvg/create_repair_order.svg";

  @override
  String get openOrders => "$imagesSvg/open_orders.svg";

  @override
  String get parts => "$imagesSvg/parts.svg";

  @override
  String get paymentDue => "$imagesSvg/payment_due.svg";

  @override
  String get readyOrder => "$imagesSvg/ready_order.svg";

  @override
  String get wipOrder => "$imagesSvg/wip_orders.svg";

  @override
  String get profilePlaceholder => "$images/profile_placeholder.png";

  @override
  String get businessComparison => "$imagesSvg/business_comparison.svg";

  @override
  String get customerSalesReport => "$imagesSvg/counter_sales_report.svg";

  @override
  String get inventoryReport => "$imagesSvg/business_comparison.svg";

  @override
  String get lostCustomerData => "$imagesSvg/lost_customer_data.svg";

  @override
  String get monthlyCustomerReport => "$imagesSvg/customer_report.svg";

  @override
  String get monthlyDetailedMISReport =>
      "$imagesSvg/monthly_detailed_mis_report.svg";

  @override
  String get pieChart => "$imagesSvg/pie_chart.svg";

  @override
  String get sparePartsConsumption => "$imagesSvg/spare.svg";

  @override
  String get totalCustomerReport => "$imagesSvg/customer_report.svg";
}
