import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class FranchiseDetails {
  String docID;
  String? ownerName;
  String? garageName;
  String? address;
  String? email;
  String? contactNumber;
  final bool? notavailable;
  final Timestamp createdAt;

  FranchiseDetails({
    required this.createdAt,
    required this.notavailable,
    required this.docID,
    this.ownerName,
    this.garageName,
    this.address,
    this.email,
    this.contactNumber,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (ownerName != null) {
      result.addAll({'ownerName': ownerName});
    }
    if (garageName != null) {
      result.addAll({'garageName': garageName});
    }
    if (address != null) {
      result.addAll({'address': address});
    }
    if (email != null) {
      result.addAll({'email': email});
    }
    if (contactNumber != null) {
      result.addAll({'contactNumber': contactNumber});
    }

    return result;
  }

  factory FranchiseDetails.fromMap(String dId, Map<String, dynamic> map) {
    return FranchiseDetails(
      ownerName: map['ownerName'],
      garageName: map['garageName'],
      address: map['address'],
      email: map['email'],
      contactNumber: map['contactNumber'],
      docID: dId,
      notavailable: map['notavailable'],
      createdAt: map["createdAt"],
    );
  }

  String toJson() => json.encode(toMap());

  // factory FranchiseDetails.fromJson(String source) =>
  //     FranchiseDetails.fromMap(snap,json.decode(source));

  factory FranchiseDetails.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return FranchiseDetails.fromMap(snap.id, data);
  }
}
