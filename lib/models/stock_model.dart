import 'dart:convert';

class StockModel {
  String? franchiseId;
  String? stockId;
  String? partId;
  double? mrp;
  double? purchasePrice;
  double? currentStock;
  double? minStock;
  double? maxStock;
  String? rackId;
  String? vendorId;
  String? comment;
  double? paidAmount;

  StockModel({
    this.franchiseId,
    this.stockId,
    this.partId,
    this.mrp,
    this.purchasePrice,
    this.currentStock,
    this.minStock,
    this.maxStock,
    this.rackId,
    this.vendorId,
    this.comment,
    this.paidAmount,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (stockId != null) {
      result.addAll({'stockId': stockId});
    }
    if (partId != null) {
      result.addAll({'partId': partId});
    }
    if (mrp != null) {
      result.addAll({'mrp': mrp});
    }
    if (purchasePrice != null) {
      result.addAll({'purchasePrice': purchasePrice});
    }
    if (currentStock != null) {
      result.addAll({'currentStock': currentStock});
    }
    if (minStock != null) {
      result.addAll({'minStock': minStock});
    }
    if (maxStock != null) {
      result.addAll({'maxStock': maxStock});
    }
    if (rackId != null) {
      result.addAll({'rackId': rackId});
    }
    if (vendorId != null) {
      result.addAll({'vendorId': vendorId});
    }
    if (comment != null) {
      result.addAll({'comment': comment});
    }
    if (paidAmount != null) {
      result.addAll({'paidAmount': paidAmount});
    }

    return result;
  }

  factory StockModel.fromMap(Map<String, dynamic> map) {
    return StockModel(
      franchiseId: map['franchiseId'],
      stockId: map['stockId'],
      partId: map['partId'],
      mrp: map['mrp']?.toDouble(),
      purchasePrice: map['purchasePrice']?.toDouble(),
      currentStock: map['currentStock']?.toDouble(),
      minStock: map['minStock']?.toDouble(),
      maxStock: map['maxStock']?.toDouble(),
      rackId: map['rackId'],
      vendorId: map['vendorId'],
      comment: map['comment'],
      paidAmount: map['paidAmount']?.toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory StockModel.fromJson(String source) =>
      StockModel.fromMap(json.decode(source));
}
