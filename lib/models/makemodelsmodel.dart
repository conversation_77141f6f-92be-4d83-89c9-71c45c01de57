import 'dart:convert';

import 'package:flutter/material.dart';

class MakeModelsModel {
  String? franchiseId;
  String? makeModelId;
  String? company;
  List<String>? models;
  MakeModelsModel({
    this.franchiseId,
    this.makeModelId,
    this.company,
    this.models,
  });

  static MakeModelsModel? fromSnap(Map<String, dynamic> snap) {
    try {
      return MakeModelsModel(
        franchiseId: snap['franchiseId'] as String?,
        makeModelId: snap['makeModelId'] as String?,
        company: snap['company'] as String?,
        models: (snap['models'] as List).map((e) => e as String).toList(),
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Map<String, dynamic> toSnap() {
    return {
      'franchiseId': franchiseId,
      'makeModelId': makeModelId,
      'company': company,
      'models': models,
    };
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (makeModelId != null) {
      result.addAll({'makeModelId': makeModelId});
    }
    if (company != null) {
      result.addAll({'company': company});
    }
    if (models != null) {
      result.addAll({'models': models});
    }

    return result;
  }

  factory MakeModelsModel.fromMap(Map<String, dynamic> map) {
    return MakeModelsModel(
      franchiseId: map['franchiseId'],
      makeModelId: map['makeModelId'],
      company: map['company'],
      models: List<String>.from(map['models']),
    );
  }

  String toJson() => json.encode(toMap());

  factory MakeModelsModel.fromJson(String source) =>
      MakeModelsModel.fromMap(json.decode(source));
}
