import 'dart:convert';

class InsuranceProviderModel {
  String? companyName;
  String? address;
  String? gstin;
  InsuranceProviderModel({
    this.companyName,
    this.address,
    this.gstin,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (companyName != null) {
      result.addAll({'companyName': companyName});
    }
    if (address != null) {
      result.addAll({'address': address});
    }
    if (gstin != null) {
      result.addAll({'gstin': gstin});
    }

    return result;
  }

  factory InsuranceProviderModel.fromMap(Map<String, dynamic> map) {
    return InsuranceProviderModel(
      companyName: map['companyName'],
      address: map['address'],
      gstin: map['gstin'],
    );
  }

  String toJson() => json.encode(toMap());

  factory InsuranceProviderModel.fromJson(String source) =>
      InsuranceProviderModel.fromMap(json.decode(source));
}
