import 'dart:convert';

class AdditionalInformationModel {
  String? fuelLevel;
  String? odometer;
  AdditionalInformationModel({
    this.fuelLevel,
    this.odometer,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (fuelLevel != null) {
      result.addAll({'fuelLevel': fuelLevel});
    }
    if (odometer != null) {
      result.addAll({'odometer': odometer});
    }

    return result;
  }

  factory AdditionalInformationModel.fromMap(Map<String, dynamic> map) {
    return AdditionalInformationModel(
      fuelLevel: map['fuelLevel'],
      odometer: map['odometer'],
    );
  }

  String toJson() => json.encode(toMap());

  factory AdditionalInformationModel.fromJson(String source) =>
      AdditionalInformationModel.fromMap(json.decode(source));
}
