import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/service_part.model.dart';

class RepairDetailsModel {
  double? servicesTotal;
  double? partsTotal;
  double? total;
  double? discount;
  double? paymentReceived;
  double? paymentDue;
  String? estimatedDelivery;

  List<ServicePartModel>? services;
  List<ServicePartModel>? parts;
  List<String>? vehicleImagesLinks;
  List<String>? repairOrderImagesLinks;
  List<String>? tags;
  List<String>? customerRemarks;
  String? customerSignatureLink;
  String? businessOwnerSignatureLink;

  RepairDetailsModel({
    this.servicesTotal,
    this.partsTotal,
    this.total,
    this.discount,
    this.paymentReceived,
    this.paymentDue,
    this.estimatedDelivery,
    this.services,
    this.parts,
    this.vehicleImagesLinks,
    this.repairOrderImagesLinks,
    this.tags,
    this.customerRemarks,
    this.customerSignatureLink,
    this.businessOwnerSignatureLink,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (servicesTotal != null) {
      result.addAll({'servicesTotal': servicesTotal});
    }
    if (partsTotal != null) {
      result.addAll({'partsTotal': partsTotal});
    }

    result.addAll({'total': total ?? 0.0});
    result.addAll({'discount': discount ?? 0.0});
    result.addAll({'paymentReceived': paymentReceived ?? 0.0});
    result.addAll({'paymentDue': paymentDue ?? total});

    if (estimatedDelivery != null) {
      result.addAll({'estimatedDelivery': estimatedDelivery});
    }
    if (services != null) {
      result.addAll({'services': services!.map((x) => x.toMap()).toList()});
    }
    if (parts != null) {
      result.addAll({'parts': parts!.map((x) => x.toMap()).toList()});
    }
    if (vehicleImagesLinks != null) {
      result.addAll({'imagesLinks': vehicleImagesLinks});
    }
    if (repairOrderImagesLinks != null) {
      result.addAll({'repairOrderImagesLinks': repairOrderImagesLinks});
    }
    if (customerSignatureLink != null) {
      result.addAll({'customerSignatureLink': customerSignatureLink});
    }
    if (tags != null) {
      result.addAll({'tags': tags});
    }

    if (customerRemarks != null) {
      result.addAll({'customerRemarks': customerRemarks});
    }

    return result;
  }

  Map<String, dynamic> toInvoiceMap() {
    final result = <String, dynamic>{};

    if (servicesTotal != null) {
      result.addAll({'servicesTotal': servicesTotal});
    }
    if (partsTotal != null) {
      result.addAll({'partsTotal': partsTotal});
    }
    if (total != null) {
      result.addAll({'total': total});
    }
    if (discount != null) {
      result.addAll({'discount': discount});
    }
    if (paymentReceived != null) {
      result.addAll({'paymentReceived': paymentReceived});
    }
    if (estimatedDelivery != null) {
      result.addAll({'estimatedDelivery': estimatedDelivery});
    }
    if (paymentDue != null) {
      result.addAll({'paymentDue': paymentDue});
    }

    if (services != null) {
      result.addAll({'services': services!.map((x) => x.toMap()).toList()});
    }

    if (parts != null) {
      result.addAll({'parts': parts!.map((x) => x.toMap()).toList()});
    }

    if (tags != null) {
      result.addAll({'tags': tags});
    }
    if (businessOwnerSignatureLink != null) {
      result.addAll({'businessOwnerSignatureLink': businessOwnerSignatureLink});
    }

    return result;
  }

  factory RepairDetailsModel.fromMap(Map<String, dynamic> map) {
    return RepairDetailsModel(
      servicesTotal: map['servicesTotal']?.toDouble(),
      partsTotal: map['partsTotal']?.toDouble(),
      total: map['total']?.toDouble(),
      discount: map['discount']?.toDouble(),
      paymentReceived: map['paymentReceived']?.toDouble(),
      paymentDue: map['paymentDue']?.toDouble(),
      estimatedDelivery: map['estimatedDelivery'],
      customerSignatureLink: map['customerSignatureLink'],
      services: map['services'] != null
          ? List<ServicePartModel>.from(
              map['services']?.map((x) => ServicePartModel.fromMap(x)))
          : null,
      parts: map['parts'] != null
          ? List<ServicePartModel>.from(
              map['parts']?.map((x) => ServicePartModel.fromMap(x)))
          : null,
      vehicleImagesLinks: map['vehicleImages'] != null
          ? List<String>.from(map['vehicleImages'])
          : [],
      repairOrderImagesLinks: map['repairOrderImagesLinks'] != null
          ? List<String>.from(map['repairOrderImagesLinks'])
          : [],
      tags: List<String>.from(map['tags']),
      customerRemarks: map['customerRemarks'] != null
          ? List<String>.from(map['customerRemarks'])
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory RepairDetailsModel.fromJson(String source) =>
      RepairDetailsModel.fromMap(json.decode(source));
}
