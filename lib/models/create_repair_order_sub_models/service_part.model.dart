import 'dart:convert';

class ServicePartModel {
  String? id;
  String? title;
  String? partServiceNumber;
  bool? isOil;
  int? quantity;
  double? rate;
  double? purchasePrice;
  double? amount;
  double? discount;
  double? servicesGst;
  double? partsGst;
  bool? completed;
  num? gstRate;

  ServicePartModel({
    this.id,
    this.title,
    this.quantity,
    this.rate,
    this.purchasePrice,
    this.amount,
    this.discount,
    this.completed,
    this.partServiceNumber,
    this.isOil,
    this.gstRate,
    this.partsGst,
    this.servicesGst,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (id != null) {
      result.addAll({'id': id});
    }
    if (title != null) {
      result.addAll({'title': title});
    }
    if (partServiceNumber != null) {
      result.addAll({'partServiceNumber': partServiceNumber});
    }
    if (quantity != null) {
      result.addAll({'quantity': quantity});
    }
    if (rate != null) {
      result.addAll({'rate': rate});
    }
    if (purchasePrice != null) {
      result.addAll({'purchasePrice': purchasePrice});
    }
    if (amount != null) {
      result.addAll({'amount': amount});
    }
    if (discount != null) {
      result.addAll({'discount': discount});
    }
    if (completed != null) {
      result.addAll({'completed': completed});
    }
    if (isOil != null) {
      result.addAll({'isOil': isOil});
    }
    if (servicesGst != null) {
      result.addAll({'servicesGst': servicesGst});
    }
    if (partsGst != null) {
      result.addAll({'partsGst': partsGst});
    }
    if (gstRate != null) {
      result.addAll({'gstRate': gstRate});
    }

    return result;
  }

  factory ServicePartModel.fromMap(Map<String, dynamic> map) {
    return ServicePartModel(
      id: map['id'],
      title: map['title'],
      partServiceNumber: map['partServiceNumber'],
      quantity: map['quantity']?.toInt(),
      rate: map['rate']?.toDouble(),
      purchasePrice: map['purchasePrice']?.toDouble(),
      amount: map['amount']?.toDouble(),
      discount: map['discount']?.toDouble(),
      servicesGst: map['servicesGst']?.toDouble(),
      partsGst: map['partsGst']?.toDouble(),
      completed: map['completed'],
      isOil: map['isOil'],
      gstRate: map.containsKey('gstRate') ? map['gstRate'] ?? 0 : 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory ServicePartModel.fromJson(String source) =>
      ServicePartModel.fromMap(json.decode(source));
}
