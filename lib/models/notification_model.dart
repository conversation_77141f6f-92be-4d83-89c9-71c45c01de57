import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String docId;
  final String userType;
  final String title;
  final String description;
  final Timestamp createdAt;
  final bool test;

  NotificationModel(
      {required this.docId,
      required this.userType,
      required this.title,
      required this.description,
      required this.createdAt,
      required this.test});

  factory NotificationModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return NotificationModel(
      docId: snap.id,
      userType: data['userType'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      createdAt: data['createdAt'] ?? Timestamp.now(),
      test: data['test'] ?? false,
    );
  }

  // Method to convert the model instance to a map for Firestore
  Map<String, dynamic> toSnap() {
    return {
      'userType': userType,
      'title': title,
      'description': description,
      'createdAt': createdAt,
      'test': test,
    };
  }
}
