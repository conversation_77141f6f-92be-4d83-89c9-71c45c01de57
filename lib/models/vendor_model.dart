import 'dart:convert';

class VendorModel {
  String? franchiseId;
  String? vendorId;
  String? name;
  String? phone;
  String? email;
  String? address;
  String? gstin;
  String? pan;
  String? referenceId;

  VendorModel({
    this.franchiseId,
    this.vendorId,
    this.name,
    this.phone,
    this.email,
    this.address,
    this.gstin,
    this.pan,
    this.referenceId,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (vendorId != null) {
      result.addAll({'vendorId': vendorId});
    }
    if (name != null) {
      result.addAll({'name': name});
    }
    if (phone != null) {
      result.addAll({'phone': phone});
    }
    if (email != null) {
      result.addAll({'email': email});
    }
    if (address != null) {
      result.addAll({'address': address});
    }
    if (gstin != null) {
      result.addAll({'gstin': gstin});
    }
    if (pan != null) {
      result.addAll({'pan': pan});
    }
    if (referenceId != null) {
      result.addAll({'referenceId': referenceId});
    }

    return result;
  }

  factory VendorModel.fromMap(Map<String, dynamic> map) {
    return VendorModel(
      franchiseId: map['franchiseId'],
      vendorId: map['vendorId'],
      name: map['name'],
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      gstin: map['gstin'],
      pan: map['pan'],
      referenceId: map['referenceId'],
    );
  }

  String toJson() => json.encode(toMap());

  factory VendorModel.fromJson(String source) =>
      VendorModel.fromMap(json.decode(source));
}
