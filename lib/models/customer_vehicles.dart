import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class CustomerVehicleModel {
  String? vehicleId;
  String? customerId;
  String? franchiseId;
  String? make;
  String? model;
  String? registrationNumber;
  String? engineNumber;
  String? chasisNumber;
  String? insurerGSTIN;
  String? insurerAddress;
  String? policyNumber;

  CustomerVehicleModel({
    this.vehicleId,
    this.customerId,
    this.franchiseId,
    this.make,
    this.model,
    this.registrationNumber,
    this.engineNumber,
    this.chasisNumber,
    this.insurerGSTIN,
    this.insurerAddress,
    this.policyNumber,
  });

  // Converts the data to a Map
  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};
    if (vehicleId != null) result.addAll({'vehicleId': vehicleId});
    if (customerId != null) result.addAll({'customerId': customerId});
    if (franchiseId != null) result.addAll({'franchiseId': franchiseId});
    if (make != null) result.addAll({'make': make});
    if (model != null) result.addAll({'model': model});
    if (registrationNumber != null)
      result.addAll({'registrationNumber': registrationNumber});
    if (engineNumber != null) result.addAll({'engineNumber': engineNumber});
    if (chasisNumber != null) result.addAll({'chasisNumber': chasisNumber});
    if (insurerGSTIN != null) result.addAll({'insurerGSTIN': insurerGSTIN});
    if (insurerAddress != null)
      result.addAll({'insurerAddress': insurerAddress});
    if (policyNumber != null) result.addAll({'policyNumber': policyNumber});
    return result;
  }

  // Creates an instance of CustomerVehicleModel from a Firestore snapshot
  factory CustomerVehicleModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return CustomerVehicleModel(
      vehicleId: data['vehicleId'],
      customerId: data['customerId'],
      franchiseId: data['franchiseId'],
      make: data['make'],
      model: data['model'],
      registrationNumber: data['registrationNumber'],
      engineNumber: data['engineNumber'],
      chasisNumber: data['chasisNumber'],
      insurerGSTIN: data['insurerGSTIN'],
      insurerAddress: data['insurerAddress'],
      policyNumber: data['policyNumber'],
    );
  }

  // Converts the object to a JSON string
  String toJson() => json.encode(toMap());

  // Creates an instance from a JSON string
  factory CustomerVehicleModel.fromJson(String source) =>
      CustomerVehicleModel.fromMap(json.decode(source));

  // Add this method to resolve the 'fromMap' error
  factory CustomerVehicleModel.fromMap(Map<String, dynamic> map) {
    return CustomerVehicleModel(
      vehicleId: map['vehicleId'],
      customerId: map['customerId'],
      franchiseId: map['franchiseId'],
      make: map['make'],
      model: map['model'],
      registrationNumber: map['registrationNumber'],
      engineNumber: map['engineNumber'],
      chasisNumber: map['chasisNumber'],
      insurerGSTIN: map['insurerGSTIN'],
      insurerAddress: map['insurerAddress'],
      policyNumber: map['policyNumber'],
    );
  }
}
