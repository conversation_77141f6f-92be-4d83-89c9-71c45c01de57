import 'dart:convert';

class ServiceModel {
  String? serviceId;
  String? franchiseId;
  String? name;
  String? serviceNumber;
  String? category;
  double? price;
  List<String>? applicableBrands;
  List<String>? applicableModels;
  num? gstRate;

  ServiceModel({
    this.serviceId,
    this.franchiseId,
    this.name,
    this.serviceNumber,
    this.category,
    this.price,
    this.applicableBrands,
    this.applicableModels,
    required this.gstRate,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (serviceId != null) {
      result.addAll({'serviceId': serviceId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (name != null) {
      result.addAll({'name': name});
    }
    if (serviceNumber != null) {
      result.addAll({'serviceNumber': serviceNumber});
    }
    if (category != null) {
      result.addAll({'category': category});
    }
    if (price != null) {
      result.addAll({'price': price});
    }
    if (applicableBrands != null) {
      result.addAll({'applicableBrands': applicableBrands});
    }
    if (applicableModels != null) {
      result.addAll({'applicableModels': applicableModels});
    }
    if (gstRate != null) {
      result.addAll({'gstRate': gstRate});
    }

    return result;
  }

  factory ServiceModel.fromMap(Map<String, dynamic> map) {
    return ServiceModel(
      serviceId: map['serviceId'],
      franchiseId: map['franchiseId'],
      name: map['name'],
      serviceNumber: map['serviceNumber'],
      category: map['category'],
      gstRate: map.containsKey('gstRate') ? map['gstRate'] : 0,
      price: map['price']?.toDouble(),
      applicableBrands: map['applicableBrands'] != null
          ? List<String>.from(map['applicableBrands'])
          : [],
      applicableModels: map['applicableModels'] != null
          ? List<String>.from(map['applicableModels'])
          : [],
    );
  }

  String toJson() => json.encode(toMap());

  factory ServiceModel.fromJson(String source) =>
      ServiceModel.fromMap(json.decode(source));
}
