import 'dart:convert';

class ServiceModel {
  String? serviceId;
  String? name;
  String? lowername; // For case-insensitive search
  String? serviceNumber;
  String? category;
  double? price;
  double? gstRate;
  DateTime? createdAt;
  DateTime? updatedAt;
  List<String>? applicableBrands;
  List<String>? applicableModels;
  ServiceModel({
    this.serviceId,
    this.name,
    this.lowername,
    this.serviceNumber,
    this.category,
    this.price,
    this.gstRate,
    this.createdAt,
    this.updatedAt,
    this.applicableBrands,
    this.applicableModels,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (serviceId != null) {
      result.addAll({'serviceId': serviceId});
    }
    if (name != null) {
      result.addAll({'name': name});
    }
    if (lowername != null) {
      result.addAll({'lowername': lowername});
    }
    if (serviceNumber != null) {
      result.addAll({'serviceNumber': serviceNumber});
    }
    if (category != null) {
      result.addAll({'category': category});
    }
    if (price != null) {
      result.addAll({'price': price});
    }
    if (gstRate != null) {
      result.addAll({'gstRate': gstRate});
    }
    if (createdAt != null) {
      result.addAll({'createdAt': createdAt!.millisecondsSinceEpoch});
    }
    if (updatedAt != null) {
      result.addAll({'updatedAt': updatedAt!.millisecondsSinceEpoch});
    }
    if (applicableBrands != null) {
      result.addAll({'applicableBrands': applicableBrands});
    }
    if (applicableModels != null) {
      result.addAll({'applicableModels': applicableModels});
    }

    return result;
  }

  factory ServiceModel.fromMap(Map<String, dynamic> map) {
    return ServiceModel(
      serviceId: map['serviceId'],
      name: map['name'],
      lowername: map['lowername'],
      serviceNumber: map['serviceNumber'],
      category: map['category'],
      price: map['price']?.toDouble(),
      gstRate: map['gstRate']?.toDouble(),
      createdAt: map['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : null,
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
      applicableBrands: map['applicableBrands'] != null
          ? List<String>.from(map['applicableBrands'])
          : [],
      applicableModels: map['applicableModels'] != null
          ? List<String>.from(map['applicableModels'])
          : [],
    );
  }

  String toJson() => json.encode(toMap());

  factory ServiceModel.fromJson(String source) =>
      ServiceModel.fromMap(json.decode(source));
}
