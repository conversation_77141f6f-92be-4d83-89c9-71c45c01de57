import 'package:cloud_firestore/cloud_firestore.dart';

class CustomersModel {
  final String docId;
  final String? username;
  final String? phone;
  final String? gstin;
  final String? email;
  final String? address;
  final String customerId;
  final String? franchiseId;
  final String? workshop;
  String? lastVisit;
  num? totalBillAmount;

  CustomersModel({
    required this.docId,
    required this.username,
    required this.phone,
    required this.customerId,
    required this.franchiseId,
    required this.address,
    required this.email,
    required this.gstin,
    required this.workshop,
    this.totalBillAmount,
    this.lastVisit,
  });

  factory CustomersModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;

    return CustomersModel(
      docId: snap.id,
      username: data['username'] ?? "-",
      phone: data['phone'] ?? "-",
      customerId: data['customerId'] ?? "-",
      franchiseId: data['franchiseId'] ?? "-",
      address: data['address'] ?? "-",
      email: data['email'] ?? "-",
      gstin: data['gstin'] ?? "-",
      workshop: data['workshop'] ?? "-",
      totalBillAmount: data['totalBillAmount'] ?? 0,
      lastVisit: data['lastVisit'] ?? "-",
    );
  }
}
