import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class AdvertiseModel {
  String docId;
  String? imageUrl;
  String? redirectLink;
  AdvertiseModel({
    required this.docId,
    this.imageUrl,
    this.redirectLink,
  });

  factory AdvertiseModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return AdvertiseModel(
      docId: snap.id,
      imageUrl: data['imageUrl'],
      redirectLink: data['redirectLink'],
    );
  }

  Map<String, dynamic> toSnap() {
    return toMap();
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (imageUrl != null) {
      result.addAll({'imageUrl': imageUrl});
    }
    if (redirectLink != null) {
      result.addAll({'redirectLink': redirectLink});
    }

    return result;
  }

  factory AdvertiseModel.fromMap(Map<String, dynamic> map) {
    return AdvertiseModel(
      imageUrl: map['imageUrl'],
      redirectLink: map['redirectLink'],
      docId: map['docId'],
    );
  }

  String toJson() => json.encode(toMap());

  factory AdvertiseModel.fromJson(String source) =>
      AdvertiseModel.fromMap(json.decode(source));
}
