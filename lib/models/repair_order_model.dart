import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:speed_force_admin/enums/enums.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/additional_information_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/repair_details_model.dart';
import 'package:speed_force_admin/models/create_repair_order_sub_models/vehicle_details_model.dart';

class RepairOrderModel {
  String? invoiceId;
  String? jobCardId;
  String? orderId;
  String? franchiseId;
  String? customerId;
  String? vehicleId;
  String? createdAt;
  OrderStatus? orderStatus;
  PaymentMode? paymentMode;
  bool? gstIncluded;
  bool? isigst;
  Timestamp? completionDate;
  CustomerDetailsModel? customerDetailsModel;
  VehicleDetailsModel? vehicleDetailsModel;
  AdditionalInformationModel? additionalInformationModel;
  RepairDetailsModel? repairDetailsModel;

  RepairOrderModel({
    this.orderId,
    this.jobCardId,
    this.invoiceId,
    this.franchiseId,
    this.customerId,
    this.vehicleId,
    this.createdAt,
    this.orderStatus,
    this.paymentMode,
    this.customerDetailsModel,
    this.vehicleDetailsModel,
    this.additionalInformationModel,
    this.repairDetailsModel,
    this.gstIncluded,
    this.isigst,
    this.completionDate,
  });

  Map<String, dynamic> toInvoiceDetailsMap() {
    final result = <String, dynamic>{};

    if (invoiceId != null) {
      result.addAll({'invoiceId': invoiceId});
    }
    if (jobCardId != null) {
      result.addAll({'jobCardId': jobCardId});
    }

    if (orderId != null) {
      result.addAll({'orderId': orderId});
    }

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }

    if (vehicleId != null) {
      result.addAll({'vehicleId': vehicleId});
    }

    if (createdAt != null) {
      result.addAll({'createdAt': createdAt});
    }
    if (isigst != null) {
      result.addAll({'isigst': isigst});
    }
    if (gstIncluded != null) {
      result.addAll({'gstIncluded': gstIncluded});
    }

    if (customerId != null) {
      result.addAll({'customerId': customerId});
    }
    if (completionDate != null) {
      result.addAll({'completionDate': completionDate});
    }

    if (repairDetailsModel != null) {
      result.addAll({'invoiceDetails': repairDetailsModel!.toInvoiceMap()});
    }

    if (orderStatus != null) {
      result.addAll({'orderStatus': orderStatus!.name});
    }

    return result;
  }

  factory RepairOrderModel.fromInvoiceDetailsMap(
      String? docId, Map<String, dynamic> map) {
    return RepairOrderModel(
      invoiceId: map['invoiceId'],
      jobCardId: map['jobCardId'],
      orderId: docId ?? map['orderId'],
      franchiseId: map['franchiseId'],
      vehicleId: map['vehicleId'],
      createdAt: map['createdAt'],
      customerId: map['customerId'],
      isigst: map['isigst'],
      completionDate: map['completionDate'],
      gstIncluded: map['gstIncluded'],
      orderStatus: map['orderStatus'] != null
          ? OrderStatus.created.getFromName(map['orderStatus'])
          : null,
      repairDetailsModel: map['invoiceDetails'] != null
          ? RepairDetailsModel.fromMap(map['invoiceDetails'])
          : null,
    );
  }

  Map<String, dynamic> toRepairDetailsMap() {
    final result = <String, dynamic>{};
    if (createdAt != null) {
      result.addAll({'createdAt': createdAt});
    }
    if (jobCardId != null) {
      result.addAll({'jobCardId': jobCardId});
    }
    if (invoiceId != null) {
      result.addAll({'invoiceId': invoiceId});
    }
    if (orderId != null) {
      result.addAll({'orderId': orderId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (customerId != null) {
      result.addAll({'customerId': customerId});
    }
    if (completionDate != null) {
      result.addAll({'completionDate': completionDate});
    }
    if (vehicleId != null) {
      result.addAll({'vehicleId': vehicleId});
    }
    if (isigst != null) {
      result.addAll({'isigst': isigst});
    }
    if (gstIncluded != null) {
      result.addAll({'gstIncluded': gstIncluded});
    }
    if (orderStatus != null) {
      result.addAll({'orderStatus': orderStatus!.name});
    }
    if (paymentMode != null) {
      result.addAll({'paymentMode': paymentMode!.name});
    }
    if (additionalInformationModel != null) {
      result.addAll(
          {'additionalInformationModel': additionalInformationModel?.toMap()});
    }
    if (repairDetailsModel != null) {
      result.addAll({'repairDetailsModel': repairDetailsModel?.toMap()});
    }

    return result;
  }

  factory RepairOrderModel.fromRepairDetailsMap(Map<String, dynamic> map) {
    return RepairOrderModel(
      orderId: map['orderId'],
      franchiseId: map['franchiseId'],
      customerId: map['customerId'],
      jobCardId: map['jobCardId'],
      invoiceId: map['invoiceId'],
      createdAt: map['createdAt'],
      completionDate: map['completionDate'],
      gstIncluded: map['gstIncluded'],
      isigst: map['isigst'],
      orderStatus: map['orderStatus'] != null
          ? OrderStatus.created.getFromName(map['orderStatus'])
          : null,
      paymentMode: map['paymentMode'] != null
          ? PaymentMode.cash.getFromName(map['paymentMode'])
          : null,
      additionalInformationModel: map['additionalInformationModel'] != null
          ? AdditionalInformationModel.fromMap(
              map['additionalInformationModel'])
          : null,
      repairDetailsModel: map['repairDetailsModel'] != null
          ? RepairDetailsModel.fromMap(map['repairDetailsModel'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (orderId != null) {
      result.addAll({'orderId': orderId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (customerId != null) {
      result.addAll({'customerId': customerId});
    }
    if (vehicleId != null) {
      result.addAll({'vehicleId': vehicleId});
    }
    if (createdAt != null) {
      result.addAll({'createdAt': createdAt});
    }
    if (jobCardId != null) {
      result.addAll({'jobCardId': jobCardId});
    }
    if (invoiceId != null) {
      result.addAll({'invoiceId': invoiceId});
    }
    if (orderStatus != null) {
      result.addAll({'orderStatus': orderStatus!.name});
    }
    if (isigst != null) {
      result.addAll({'isigst': isigst});
    }
    if (gstIncluded != null) {
      result.addAll({'gstIncluded': gstIncluded});
    }
    if (completionDate != null) {
      result.addAll({'completionDate': completionDate});
    }
    if (paymentMode != null) {
      result.addAll({'paymentMode': paymentMode!.name});
    }
    if (customerDetailsModel != null) {
      result.addAll({'customerDetailsModel': customerDetailsModel!.toMap()});
    }
    if (vehicleDetailsModel != null) {
      result.addAll({'vehicleDetailsModel': vehicleDetailsModel!.toMap()});
    }
    if (additionalInformationModel != null) {
      result.addAll(
          {'additionalInformationModel': additionalInformationModel!.toMap()});
    }
    if (repairDetailsModel != null) {
      result.addAll({'repairDetailsModel': repairDetailsModel!.toMap()});
    }

    return result;
  }

  factory RepairOrderModel.fromMap(String? docId, Map<String, dynamic> map) {
    return RepairOrderModel(
      orderId: docId ?? map['orderId'],
      franchiseId: map['franchiseId'],
      customerId: map['customerId'],
      vehicleId: map['vehicleId'],
      createdAt: map['createdAt'],
      jobCardId: map['jobCardId'],
      invoiceId: map['invoiceId'],
      completionDate: map['completionDate'],
      orderStatus: map['orderStatus'] != null
          ? OrderStatus.created.getFromName(map['orderStatus'])
          : null,
      gstIncluded: map['gstIncluded'],
      isigst: map['isigst'],
      paymentMode: map['paymentMode'] != null
          ? PaymentMode.cash.getFromName(map['paymentMode'])
          : null,
      customerDetailsModel: map['customerDetailsModel'] != null
          ? CustomerDetailsModel.fromMap(map['customerDetailsModel'])
          : null,
      vehicleDetailsModel: map['vehicleDetailsModel'] != null
          ? VehicleDetailsModel.fromMap(map['vehicleDetailsModel'])
          : null,
      additionalInformationModel: map['additionalInformationModel'] != null
          ? AdditionalInformationModel.fromMap(
              map['additionalInformationModel'])
          : null,
      repairDetailsModel: map['repairDetailsModel'] != null
          ? RepairDetailsModel.fromMap(map['repairDetailsModel'])
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  // factory RepairOrderModel.fromJson(String source) =>
  //     RepairOrderModel.fromMap(null, json.decode(source));
}
