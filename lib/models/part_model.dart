// import 'dart:convert';

// class PartModel {
//   String? partId;
//   String? franchiseId;
//   String? name;
//   String? partNumber;
//   String? category;
//   String? manufacturer;
//   List<String>? applicableBrands;
//   List<String>? applicableModels;
//   double? quantity;

//   PartModel({
//     this.partId,
//     this.franchiseId,
//     this.name,
//     this.partNumber,
//     this.category,
//     this.manufacturer,
//     this.applicableBrands,
//     this.applicableModels,
//     this.quantity,
//   });

//   Map<String, dynamic> toMap() {
//     final result = <String, dynamic>{};

//     if (partId != null) {
//       result.addAll({'partId': partId});
//     }
//     if (franchiseId != null) {
//       result.addAll({'franchiseId': franchiseId});
//     }
//     if (name != null) {
//       result.addAll({'name': name});
//     }
//     if (partNumber != null) {
//       result.addAll({'partNumber': partNumber});
//     }
//     if (category != null) {
//       result.addAll({'category': category});
//     }

//     if (manufacturer != null) {
//       result.addAll({'manufacturer': manufacturer});
//     }

//     if (applicableBrands != null) {
//       result.addAll({'applicableBrands': applicableBrands});
//     }
//     if (applicableModels != null) {
//       result.addAll({'applicableModels': applicableModels});
//     }

//     return result;
//   }

//   factory PartModel.fromMap(Map<String, dynamic> map) {
//     return PartModel(
//       partId: map['partId'],
//       franchiseId: map['franchiseId'],
//       name: map['name'] ?? "",
//       partNumber: map['partNumber'] ?? "",
//       category: map['category'],
//       manufacturer: map['manufacturer'],
//       applicableBrands: map['applicableBrands'] != null
//           ? List<String>.from(map['applicableBrands'])
//           : [],
//       applicableModels: map['applicableModels'] != null
//           ? List<String>.from(map['applicableModels'])
//           : [],
//     );
//   }

//   Map<String, dynamic> toPurchaseOrderMap() {
//     final result = <String, dynamic>{};
//     if (partId != null) {
//       result.addAll({'partId': partId});
//     }
//     if (name != null) {
//       result.addAll({'name': name});
//     }
//     if (name != null) {
//       result.addAll({'quantity': quantity});
//     }

//     return result;
//   }

//   factory PartModel.fromPurchaseOrderMap(Map<String, dynamic> map) {
//     return PartModel(
//       partId: map['partId'] ?? "",
//       name: map['name'] ?? "",
//       quantity: map['quantity'] != null ? map['quantity']?.toDouble() : 0,
//     );
//   }

//   String toJson() => json.encode(toMap());

//   factory PartModel.fromJson(String source) =>
//       PartModel.fromMap(json.decode(source));
// }
import 'dart:convert';

class PartModel {
  String? partId;
  String? franchiseId;
  String? name;
  String? partNumber;
  String? category;
  String? manufacturer;
  double? mrp;
  double? purchasePrice;
  List<String>? applicableBrands;
  List<String>? applicableModels;
  double? quantity;
  bool? isOil;
  num? gstRate;

  PartModel({
    this.partId,
    this.franchiseId,
    this.name,
    this.partNumber,
    this.category,
    this.manufacturer,
    this.applicableBrands,
    this.applicableModels,
    this.quantity,
    this.mrp,
    this.purchasePrice,
    this.isOil,
    this.gstRate,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (partId != null) {
      result.addAll({'partId': partId});
    }
    if (isOil != null) {
      result.addAll({'isOil': isOil});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (name != null) {
      result.addAll({'name': name});
    }
    if (partNumber != null) {
      result.addAll({'partNumber': partNumber});
    }
    if (category != null) {
      result.addAll({'category': category});
    }

    if (manufacturer != null) {
      result.addAll({'manufacturer': manufacturer});
    }
    if (mrp != null) {
      result.addAll({'mrp': mrp});
    }
    if (purchasePrice != null) {
      result.addAll({'purchasePrice': purchasePrice});
    }

    if (applicableBrands != null) {
      result.addAll({'applicableBrands': applicableBrands});
    }
    if (applicableModels != null) {
      result.addAll({'applicableModels': applicableModels});
    }
    if (gstRate != null) {
      result.addAll({'gstRate': gstRate});
    }

    return result;
  }

  factory PartModel.fromMap(Map<String, dynamic> map) {
    return PartModel(
      partId: map['partId'],
      isOil: map['isOil'],
      franchiseId: map['franchiseId'],
      name: map['name'] ?? "",
      partNumber: map['partNumber'] ?? "",
      category: map['category'],
      gstRate: map.containsKey('gstRate') ? map['gstRate'] : 0,
      mrp: map['mrp']?.toDouble(),
      purchasePrice: map['purchasePrice']?.toDouble(),
      manufacturer: map['manufacturer'],
      applicableBrands: map['applicableBrands'] != null
          ? List<String>.from(map['applicableBrands'])
          : [],
      applicableModels: map['applicableModels'] != null
          ? List<String>.from(map['applicableModels'])
          : [],
    );
  }

  Map<String, dynamic> toPurchaseOrderMap() {
    final result = <String, dynamic>{};
    if (partId != null) {
      result.addAll({'partId': partId});
    }
    if (isOil != null) {
      result.addAll({'isOil': isOil});
    }
    if (name != null) {
      result.addAll({'name': name});
    }
    if (name != null) {
      result.addAll({'quantity': quantity});
    }
    if (name != null) {
      result.addAll({'purchasePrice': purchasePrice});
    }
    if (name != null) {
      result.addAll({'mrp': mrp});
    }

    return result;
  }

  factory PartModel.fromPurchaseOrderMap(Map<String, dynamic> map) {
    return PartModel(
      partId: map['partId'] ?? "",
      isOil: map['isOil'] ?? false,
      name: map['name'] ?? "",
      quantity: map['quantity'] != null ? map['quantity']?.toDouble() : 0,
      purchasePrice:
          map['purchasePrice'] != null ? map['purchasePrice']?.toDouble() : 0.0,
      mrp: map['mrp'] != null ? map['mrp']?.toDouble() : 0.0,
    );
  }

  String toJson() => json.encode(toMap());

  factory PartModel.fromJson(String source) =>
      PartModel.fromMap(json.decode(source));
}
