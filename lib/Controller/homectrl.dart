import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_admin/models/advertise_model.dart';
import 'package:speed_force_admin/models/franchise_details.dart';
import 'package:speed_force_admin/shared/firebase.dart';

import '../models/customer_vehicles.dart';
import '../models/makemodelsmodel.dart';

class HomeCtrl extends GetxController {
  List<FranchiseDetails> franchises = [];
  List<MakeModelsModel> makecreatemodel = [];
  List<AdvertiseModel> advertise = [];
  List<CustomerVehicleModel> cusvehicle = [];

  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? franchisesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      vehiclecompaniesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? advertisesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? cusvehicleStream;
  bool addFranchise = false;

  @override
  void onInit() {
    super.onInit();
    getfranchisesData();
    getvehiclecompaniesdata();
    getadvertisedata();
    getcusvehicledata();
    settings();
  }

  settings() async {
    DocumentSnapshot<Map<String, dynamic>> appSettingsDocSnapshot =
        await FirebaseFirestore.instance
            .collection('AppSettings')
            .doc("versionSettings")
            .get();
    addFranchise = appSettingsDocSnapshot.data()?['addFranchise'];
    print(addFranchise);
  }

  getcusvehicledata() {
    try {
      cusvehicleStream?.cancel();
      cusvehicleStream = FBFireStore.customersvehicles.snapshots().listen(
        (event) {
          cusvehicle =
              event.docs.map((e) => CustomerVehicleModel.fromSnap(e)).toList();
          // print("cus vehicles : $cusvehicle");

          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getfranchisesData() {
    try {
      franchisesStream?.cancel();
      franchisesStream = FBFireStore.franchises.snapshots().listen((event) {
        franchises =
            event.docs.map((e) => FranchiseDetails.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getvehiclecompaniesdata() {
    try {
      vehiclecompaniesStream?.cancel();
      vehiclecompaniesStream = FBFireStore.vehiclecompanies
          .where("franchiseId", isNull: true)
          .snapshots()
          .listen((event) {
        makecreatemodel = event.docs
            .map((e) => MakeModelsModel.fromSnap(e.data()))
            .whereType<MakeModelsModel>()
            .toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getadvertisedata() {
    try {
      advertisesStream?.cancel();
      advertisesStream = FBFireStore.advertises.snapshots().listen((event) {
        advertise = event.docs.map((e) => AdvertiseModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {}
  }

  // getcustomersData() {
  //   try {
  //     customersStream?.cancel();
  //     customersStream = FBFireStore.customers.snapshots().listen((event) {
  //       customers = event.docs.map((e) => CustomersModel.fromSnap(e)).toList();
  //       // print(franchises.toString());
  //       update();
  //     });
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }
}
