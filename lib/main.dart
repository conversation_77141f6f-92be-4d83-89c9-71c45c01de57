import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_admin/configurations/app_configurations.dart';
import 'package:speed_force_admin/shared/router.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await initialBaseSetup();
  // setPathUrlStrategy();
  runApp(const MyApp());
}

//garage = workshop

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: appRouter,
      debugShowCheckedModeBanner: false,
      title: 'Speed Force Admin',
      scaffoldMessengerKey: scaffoldMessengerKey,
      theme: ThemeData(colorSchemeSeed: Colors.red),
    );
  }
}

final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
