import 'package:flutter/material.dart';
import 'package:speed_force_admin/shared/methods.dart';

class TextFieldHeader extends StatelessWidget {
  const TextFieldHeader({
    super.key,
    required this.title,
  });
  final String title;
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(bottom: 4.0),
        child: Text(title,
            style: textStyle().copyWith(
              fontWeight: FontWeight.w500,
            )));
  }
}

class HeaderTextField extends StatelessWidget {
  const HeaderTextField({
    super.key,
    required this.lnamectrl,
    required this.title,
    required this.subtitle,
  });

  final TextEditingController lnamectrl;
  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 380),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text(
              title,
              style: textStyle().copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          TextField(
            controller: lnamectrl,
            decoration: textfieldDecoration().copyWith(
              hintText: subtitle,
              hintStyle: const TextStyle(
                color: Color.fromARGB(255, 157, 156, 156),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HeightBox extends SizedBox {
  const HeightBox(double height, {super.key}) : super(height: height);
}

class WidthBox extends SizedBox {
  const WidthBox(double width, {super.key}) : super(width: width);
}
