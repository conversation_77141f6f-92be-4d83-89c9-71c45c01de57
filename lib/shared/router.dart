import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:speed_force_admin/Controller/homectrl.dart';
import 'package:speed_force_admin/Views/auth/auth.dart';
import 'package:speed_force_admin/Views/customers/customers.dart';
import 'package:speed_force_admin/Views/franchises/franchises.dart';
import 'package:speed_force_admin/Views/parts/parts.dart';
import 'package:speed_force_admin/Views/report/report.dart';
import 'package:speed_force_admin/Views/report/sales_report.dart';
import 'package:speed_force_admin/Views/services/services.dart';
import 'package:speed_force_admin/Views/settings/settings.dart';
import 'package:speed_force_admin/Views/vehicles/vehicle.dart';
import 'package:speed_force_admin/wrapper.dart';
import '../Views/advertises-banners/banners.dart';
import '../Views/make-create/makecreatemodel.dart';
import '../Views/notifications/notifications.dart';
import 'methods.dart';

const homeRoute = Routes.franchises;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: homeRoute,
  routes: _routes,
  redirect: redirector,
  // errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  if (isLoggedIn()) {
    if (state.fullPath == Routes.auth) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().update();
      return null;
    }
  } else {
    return Routes.auth;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // GoRoute(
    //   path: '${Routes.category}/:name',
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       NoTransitionPage(
    //           child: CategoryWid(
    //     name: state.pathParameters['name'] ?? "",
    //   )),
    // ),
    ShellRoute(
        builder: (context, state, child) {
          if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
          return Wrapper(child: child);
          // return DashboardScreen(child: child);
        },
        routes: [
          GoRoute(
            path: Routes.franchises,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Franchises()),
          ),
          GoRoute(
            path: Routes.customers,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Customers()),
          ),
          GoRoute(
            path: Routes.services,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Services()),
          ),
          GoRoute(
            path: Routes.report,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Report()),
          ),
          GoRoute(
            path: Routes.parts,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Parts()),
          ),
          GoRoute(
            path: Routes.vehicles,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Vehicles()),
          ),
          GoRoute(
            path: Routes.settings,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Settings()),
          ),
          GoRoute(
            path: Routes.makeCreate,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Makecreatemodel()),
          ),
          GoRoute(
            path: Routes.banners,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Banners()),
          ),
          GoRoute(
            path: Routes.notifications,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: Notifications()),
          ),
          GoRoute(
            path: Routes.salesreport,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: SalesReport()),
          ),
          // GoRoute(
          //   path: Routes.contactnumber,
          //   pageBuilder: (BuildContext context, GoRouterState state) =>
          //       const NoTransitionPage(child: ContactNumber()),
          // ),
          // GoRoute(
          //   path: Routes.email,
          //   pageBuilder: (BuildContext context, GoRouterState state) =>
          //       const NoTransitionPage(child: Email()),
          // ),
          // GoRoute(
          //   path: Routes.garagename,
          //   pageBuilder: (BuildContext context, GoRouterState state) =>
          //       const NoTransitionPage(child: GarageName()),
          // ),
          // GoRoute(
          //   path: Routes.ownername,
          //   pageBuilder: (BuildContext context, GoRouterState state) =>
          //       const NoTransitionPage(child: OwnerName()),
          // ),
        ]),
    GoRoute(
      path: Routes.auth,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: SignInPage()),
    ),
    // GoRoute(
    //   path: Routes.dashboard,
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       const NoTransitionPage(child: DashboardScreen()),
    // ),
  ];
}

class Routes {
  static const auth = '/auth';
  static const franchises = '/franchises';
  static const customers = '/customers';
  static const services = '/services';
  static const report = '/report';
  static const parts = '/parts';
  static const vehicles = '/vehicles';
  static const settings = '/settings';
  static const makeCreate = '/make-create';
  static const banners = '/banners';
  static const notifications = '/notifications';
  static const salesreport = '/salesreport';
}

  // return isLoggedIn()
  //     ? (state.uri.path == Routes.auth ? Routes.home : null)
  //     : Routes.auth;