import 'dart:math';

import 'package:flutter/material.dart';
import 'package:speed_force_admin/main.dart';

import 'firebase.dart';

const _chars = '1234567890';
Random _rnd = Random();

showAppSnackbar(String text,
    {SnackBarAction? action,
    Duration duration = const Duration(milliseconds: 1500)}) {
  scaffoldMessengerKey.currentState?.showSnackBar(SnackBar(
    content: Text(text),
    action: action,
    duration: duration,
  ));
}

InputDecoration textfieldDecoration() {
  return InputDecoration(
    enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Color.fromARGB(255, 209, 209, 209))),
    focusedBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Color.fromARGB(255, 209, 209, 209))),
    fillColor: Colors.white,
    filled: true,
    border: OutlineInputBorder(
      borderSide: BorderSide.none,
      borderRadius: BorderRadius.circular(6),
    ),
  );
}

TextStyle textStyle() {
  return const TextStyle(
      fontWeight: FontWeight.w500,
      fontSize: 18,
      color: Color.fromARGB(255, 118, 116, 116));
}

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

String phoneNo(String no) {
  return no.contains("+91") ? no : '+91$no';
}

bool isLoggedIn() => FBAuth.auth.currentUser != null;
Future<dynamic> paymentAlertDialog(BuildContext context) {
  return showDialog(
    context: context,
    builder: (context) {
      return const AlertDialog(
        title: Padding(
          padding: EdgeInsets.only(
            left: 30,
            right: 30,
            top: 10,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Under Maintenance!!"),
            ],
          ),
        ),
        content: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 30,
                right: 30,
                bottom: 10,
              ),
              child: Text("Please contact your development team."),
            ),
          ],
        ),
      );
    },
  );
}
